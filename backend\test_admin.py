#!/usr/bin/env python3
"""
Test admin endpoints specifically
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:5000/api"
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "admin123"

def get_admin_token():
    """Get admin token"""
    login_data = {
        "email": ADMIN_EMAIL,
        "password": ADMIN_PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Logged in as: {data['user']['name']} (Role: {data['user']['role']})")
        return data['access_token']
    else:
        print(f"❌ Login failed: {response.status_code} - {response.text}")
        return None

def test_debug_endpoint(token):
    """Test the debug endpoint"""
    print("🔍 Testing debug endpoint...")
    
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        response = requests.get(f"{BASE_URL}/admin/debug/db", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Debug endpoint works!")
            print(f"   Students: {len(data['students'])}")
            print(f"   Teachers: {len(data['teachers'])}")
            print(f"   Subjects: {len(data['subjects'])}")
            return True
        else:
            print(f"❌ Debug endpoint failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_current_user(token):
    """Test the current user endpoint"""
    print("👤 Testing current user endpoint...")
    
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Current user: {data['name']} (Role: {data['role']})")
            return True
        else:
            print(f"❌ Current user failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🔧 Testing Admin Endpoints")
    print("=" * 30)
    
    # Get token
    token = get_admin_token()
    if not token:
        return
    
    # Test current user
    test_current_user(token)
    
    # Test debug endpoint
    test_debug_endpoint(token)

if __name__ == "__main__":
    main()
