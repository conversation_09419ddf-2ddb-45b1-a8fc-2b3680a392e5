import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext.jsx';
import {
  HomeIcon,
  DocumentTextIcon,
  UserGroupIcon,
  BookOpenIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  Bars3Icon,
  XMarkIcon,
} from '@heroicons/react/24/outline';

const Layout = ({ children }) => {
  const { user, logout, isAdmin, isTeacher } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const navigation = [
    { name: 'Dashboard', href: '/', icon: HomeIcon, current: location.pathname === '/' },
    { 
      name: 'Grade Papers', 
      href: '/grade', 
      icon: DocumentTextIcon, 
      current: location.pathname === '/grade',
      show: isTeacher 
    },
    { 
      name: 'Batch Grading', 
      href: '/batch', 
      icon: ChartBarIcon, 
      current: location.pathname === '/batch',
      show: isTeacher 
    },
    { 
      name: 'Users', 
      href: '/users', 
      icon: UserGroupIcon, 
      current: location.pathname === '/users',
      show: isAdmin 
    },
    { 
      name: 'Subjects', 
      href: '/subjects', 
      icon: BookOpenIcon, 
      current: location.pathname === '/subjects',
      show: isAdmin 
    },
    { 
      name: 'Settings', 
      href: '/settings', 
      icon: Cog6ToothIcon, 
      current: location.pathname === '/settings' 
    },
  ].filter(item => item.show !== false);

  const Sidebar = ({ mobile = false }) => (
    <div className={`flex flex-col ${mobile ? 'h-full' : 'h-screen'} bg-gradient-to-b from-white to-gray-50`}>
      <div className="flex items-center justify-between h-20 px-6 bg-gradient-to-r from-primary-600 via-primary-700 to-primary-800 shadow-large">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <h1 className="text-2xl font-bold text-white tracking-tight">
              AH<span className="text-primary-200">correcting</span>
            </h1>
          </div>
        </div>
        {mobile && (
          <button
            type="button"
            className="text-white hover:text-primary-200 transition-colors duration-200 p-2 rounded-lg hover:bg-white/10"
            onClick={() => setSidebarOpen(false)}
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        )}
      </div>
      
      <div className="flex-1 flex flex-col overflow-y-auto border-r border-gray-200/50">
        <nav className="flex-1 px-4 py-6 space-y-2">
          {navigation.map((item) => (
            <Link
              key={item.name}
              to={item.href}
              className={`sidebar-item ${
                item.current
                  ? 'sidebar-item-active'
                  : 'sidebar-item-inactive'
              }`}
              onClick={() => mobile && setSidebarOpen(false)}
            >
              <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
              <span className="truncate">{item.name}</span>
            </Link>
          ))}
        </nav>
        
        <div className="flex-shrink-0 border-t border-gray-200/50 p-6 bg-gradient-to-r from-gray-50 to-white">
          <div className="flex items-center mb-4">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 rounded-full bg-gradient-to-r from-primary-500 to-primary-600 flex items-center justify-center shadow-medium">
                <span className="text-sm font-bold text-white">
                  {user?.name?.charAt(0).toUpperCase()}
                </span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-semibold text-gray-900">{user?.name}</p>
              <p className="text-xs text-gray-500 capitalize font-medium">{user?.role}</p>
            </div>
          </div>
          <button
            onClick={handleLogout}
            className="w-full flex items-center px-4 py-3 text-sm text-gray-600 hover:text-gray-900 hover:bg-white/80 rounded-xl transition-all duration-200 font-medium hover:shadow-soft"
          >
            <ArrowRightOnRectangleIcon className="mr-3 h-5 w-5" />
            Sign out
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="h-screen flex overflow-hidden bg-gradient-to-br from-gray-50 via-white to-gray-100">
      {/* Desktop sidebar */}
      <div className="hidden md:flex md:flex-shrink-0">
        <div className="flex flex-col w-72">
          <Sidebar />
        </div>
      </div>

      {/* Mobile sidebar */}
      {sidebarOpen && (
        <div className="fixed inset-0 flex z-50 md:hidden">
          <div className="fixed inset-0 bg-gray-900/50 backdrop-blur-sm" onClick={() => setSidebarOpen(false)} />
          <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white shadow-large">
            <Sidebar mobile />
          </div>
        </div>
      )}

      {/* Main content */}
      <div className="flex flex-col w-0 flex-1 overflow-hidden">
        {/* Mobile header */}
        <div className="md:hidden">
          <div className="relative z-10 flex-shrink-0 flex h-20 bg-gradient-to-r from-white to-gray-50 shadow-soft border-b border-gray-200/50">
            <button
              type="button"
              className="px-6 border-r border-gray-200/50 text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 md:hidden transition-colors duration-200"
              onClick={() => setSidebarOpen(true)}
            >
              <Bars3Icon className="h-6 w-6" />
            </button>
            <div className="flex-1 px-6 flex justify-between items-center">
              <div className="flex-1 flex items-center">
                <h1 className="text-xl font-bold gradient-text">AHcorrecting</h1>
              </div>
              <div className="ml-4 flex items-center md:ml-6">
                <div className="flex items-center">
                  <div className="h-10 w-10 rounded-full bg-gradient-to-r from-primary-500 to-primary-600 flex items-center justify-center shadow-medium">
                    <span className="text-sm font-bold text-white">
                      {user?.name?.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <span className="ml-3 text-sm font-semibold text-gray-700">{user?.name}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1 relative overflow-y-auto focus:outline-none bg-gradient-to-br from-gray-50/50 to-white/50">
          <div className="py-8">
            <div className="max-w-7xl mx-auto px-6 sm:px-8 md:px-10">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Layout;
