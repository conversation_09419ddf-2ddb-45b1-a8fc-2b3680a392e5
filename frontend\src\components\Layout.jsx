import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext.jsx';
import {
  HomeIcon,
  DocumentTextIcon,
  UserGroupIcon,
  BookOpenIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  Bars3Icon,
  XMarkIcon,
} from '@heroicons/react/24/outline';

const Layout = ({ children }) => {
  const { user, logout, isAdmin, isTeacher } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const navigation = [
    { name: 'Dashboard', href: '/', icon: HomeIcon, current: location.pathname === '/' },
    { 
      name: 'Grade Papers', 
      href: '/grade', 
      icon: DocumentTextIcon, 
      current: location.pathname === '/grade',
      show: isTeacher 
    },
    { 
      name: 'Batch Grading', 
      href: '/batch', 
      icon: ChartBarIcon, 
      current: location.pathname === '/batch',
      show: isTeacher 
    },
    { 
      name: 'Users', 
      href: '/users', 
      icon: UserGroupIcon, 
      current: location.pathname === '/users',
      show: isAdmin 
    },
    { 
      name: 'Subjects', 
      href: '/subjects', 
      icon: BookOpenIcon, 
      current: location.pathname === '/subjects',
      show: isAdmin 
    },
    { 
      name: 'Settings', 
      href: '/settings', 
      icon: Cog6ToothIcon, 
      current: location.pathname === '/settings' 
    },
  ].filter(item => item.show !== false);

  const Sidebar = ({ mobile = false }) => (
    <div className="sidebar">
      <div className="sidebar-header">
        <div className="flex items-center">
          <div>
            <h1 className="sidebar-logo">
              AH<span>correcting</span>
            </h1>
          </div>
        </div>
        {mobile && (
          <button
            type="button"
            className="modal-close"
            onClick={() => setSidebarOpen(false)}
          >
            <XMarkIcon className="nav-icon" />
          </button>
        )}
      </div>
      
      <div className="sidebar-nav">
        {navigation.map((item) => (
          <Link
            key={item.name}
            to={item.href}
            className={`nav-item ${
              item.current
                ? 'active'
                : 'inactive'
            }`}
            onClick={() => mobile && setSidebarOpen(false)}
          >
            <item.icon className="nav-icon" />
            <span>{item.name}</span>
          </Link>
        ))}
      </div>
        
      <div className="sidebar-footer">
        <div className="user-info">
          <div className="user-avatar">
            {user?.name?.charAt(0).toUpperCase()}
          </div>
          <div className="user-details">
            <div className="user-name">{user?.name}</div>
            <div className="user-role">{user?.role}</div>
          </div>
        </div>
        <button
          onClick={handleLogout}
          className="logout-btn"
        >
          <ArrowRightOnRectangleIcon className="nav-icon" />
          Sign out
        </button>
      </div>
    </div>
  );

  return (
    <div className="app-container">
      {/* Desktop sidebar */}
      <div className="sidebar" style={{ display: window.innerWidth >= 768 ? 'flex' : 'none' }}>
        <Sidebar />
      </div>

      {/* Mobile sidebar */}
      {sidebarOpen && (
        <div className="modal-overlay" style={{ display: window.innerWidth < 768 ? 'flex' : 'none' }}>
          <div className="sidebar" style={{ maxWidth: '320px', width: '100%' }}>
            <Sidebar mobile />
          </div>
        </div>
      )}

      {/* Main content */}
      <div className="main-content">
        {/* Mobile header */}
        <div className="mobile-header">
          <button
            type="button"
            className="btn-secondary"
            onClick={() => setSidebarOpen(true)}
            style={{ padding: '8px' }}
          >
            <Bars3Icon className="nav-icon" />
          </button>
          <div className="flex justify-between items-center w-full">
            <h1 className="sidebar-logo" style={{ background: 'linear-gradient(135deg, var(--primary-600) 0%, #d946ef 100%)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>
              AHcorrecting
            </h1>
            <div className="flex items-center">
              <div className="user-avatar">
                {user?.name?.charAt(0).toUpperCase()}
              </div>
              <span className="user-name" style={{ marginLeft: '12px' }}>{user?.name}</span>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="page-content">
          <div className="page-container">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default Layout;
