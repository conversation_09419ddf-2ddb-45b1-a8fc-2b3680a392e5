#!/usr/bin/env python3
"""
Database migration script to update schema
This script will:
1. Add the 'code' column to the Subject table
2. Create the Grade and Enrollment tables
3. Handle any schema updates safely
"""

import sqlite3
import os
from app import app, db

def migrate_database():
    """Migrate the database schema"""
    
    # Get the database path
    db_path = 'instance/database.db'
    
    if not os.path.exists(db_path):
        print("❌ Database file not found. Please run the app first to create the database.")
        return False
    
    try:
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 Checking current database schema...")
        
        # Check if 'code' column exists in subject table
        cursor.execute("PRAGMA table_info(subject)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'code' not in columns:
            print("➕ Adding 'code' column to subject table...")
            cursor.execute("ALTER TABLE subject ADD COLUMN code VARCHAR(20)")
            print("✅ Added 'code' column to subject table")
        else:
            print("✅ 'code' column already exists in subject table")
        
        # Check if grade table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='grade'")
        if not cursor.fetchone():
            print("➕ Creating grade table...")
            cursor.execute("""
                CREATE TABLE grade (
                    id INTEGER PRIMARY KEY,
                    student_id INTEGER NOT NULL,
                    subject_id INTEGER NOT NULL,
                    teacher_id INTEGER NOT NULL,
                    correction_id INTEGER,
                    score FLOAT NOT NULL,
                    percentage INTEGER NOT NULL,
                    matches BOOLEAN NOT NULL,
                    student_text TEXT,
                    key_text TEXT,
                    notes TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (student_id) REFERENCES user(id),
                    FOREIGN KEY (subject_id) REFERENCES subject(id),
                    FOREIGN KEY (teacher_id) REFERENCES user(id),
                    FOREIGN KEY (correction_id) REFERENCES correction(id)
                )
            """)
            print("✅ Created grade table")
        else:
            print("✅ Grade table already exists")
        
        # Check if enrollment table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='enrollment'")
        if not cursor.fetchone():
            print("➕ Creating enrollment table...")
            cursor.execute("""
                CREATE TABLE enrollment (
                    id INTEGER PRIMARY KEY,
                    student_id INTEGER NOT NULL,
                    subject_id INTEGER NOT NULL,
                    teacher_id INTEGER NOT NULL,
                    enrolled_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (student_id) REFERENCES user(id),
                    FOREIGN KEY (subject_id) REFERENCES subject(id),
                    FOREIGN KEY (teacher_id) REFERENCES user(id),
                    UNIQUE(student_id, subject_id)
                )
            """)
            print("✅ Created enrollment table")
        else:
            print("✅ Enrollment table already exists")
        
        # Commit changes
        conn.commit()
        conn.close()
        
        print("\n🎉 Database migration completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def add_sample_data():
    """Add some sample enrollment data for testing"""
    try:
        with app.app_context():
            from models import User, Subject, Enrollment
            
            # Get first student and subject for testing
            student = User.query.filter_by(role='student').first()
            subject = Subject.query.first()
            teacher = User.query.filter_by(role='teacher').first()
            
            if student and subject and teacher:
                # Check if enrollment already exists
                existing = Enrollment.query.filter_by(
                    student_id=student.id,
                    subject_id=subject.id
                ).first()
                
                if not existing:
                    enrollment = Enrollment(
                        student_id=student.id,
                        subject_id=subject.id,
                        teacher_id=teacher.id,
                        is_active=True
                    )
                    db.session.add(enrollment)
                    db.session.commit()
                    print(f"✅ Added sample enrollment: {student.name} -> {subject.name}")
                else:
                    print("✅ Sample enrollment already exists")
            else:
                print("ℹ️  No sample data added (missing student, subject, or teacher)")
                
    except Exception as e:
        print(f"⚠️  Could not add sample data: {e}")

if __name__ == "__main__":
    print("🚀 Starting database migration...")
    
    success = migrate_database()
    
    if success:
        print("\n📊 Adding sample enrollment data...")
        add_sample_data()
        
        print("\n✅ Migration completed successfully!")
        print("\nYou can now:")
        print("- Start the backend server: python app.py")
        print("- Use the Grade Papers interface with student dropdowns")
        print("- View enrolled subjects in the Student Dashboard")
        print("- Manage enrollments through the admin interface")
    else:
        print("\n❌ Migration failed!")
        print("Please check the error messages above and try again.")
