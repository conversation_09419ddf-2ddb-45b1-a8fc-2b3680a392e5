#!/usr/bin/env python3
"""
Setup and test script for AHcorrecting backend
This script helps with:
- Installing dependencies
- Setting up the database
- Creating admin user
- Running tests
"""

import os
import sys
import subprocess
import sqlite3
from pathlib import Path

def run_command(command, description):
    """Run a command and return success status"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Command: {command}")
        print(f"   Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible")
        print("   AHcorrecting requires Python 3.8 or higher")
        return False

def install_dependencies():
    """Install Python dependencies"""
    print("📦 Installing dependencies...")
    
    # Check if pip is available
    if not run_command("pip --version", "Checking pip"):
        print("❌ pip is not available. Please install pip first.")
        return False
    
    # Install requirements
    if os.path.exists("requirements.txt"):
        return run_command("pip install -r requirements.txt", "Installing requirements")
    else:
        print("❌ requirements.txt not found")
        return False

def setup_database():
    """Set up the database"""
    print("🗄️ Setting up database...")
    
    try:
        # Import app to create database
        from app import create_app
        from models import db
        
        app = create_app()
        with app.app_context():
            db.create_all()
            print("✅ Database tables created successfully")
        
        return True
    except Exception as e:
        print(f"❌ Database setup failed: {str(e)}")
        return False

def create_admin_user():
    """Create admin user"""
    print("👤 Creating admin user...")
    
    try:
        # Run the create_admin script
        return run_command("python create_admin.py", "Creating admin user")
    except Exception as e:
        print(f"❌ Admin user creation failed: {str(e)}")
        return False

def check_tesseract():
    """Check if Tesseract OCR is installed"""
    print("👁️ Checking Tesseract OCR...")
    
    try:
        result = subprocess.run("tesseract --version", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ Tesseract found: {version_line}")
            return True
        else:
            print("❌ Tesseract not found")
            print("   Please install Tesseract OCR:")
            print("   - Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki")
            print("   - macOS: brew install tesseract")
            print("   - Ubuntu: sudo apt install tesseract-ocr")
            return False
    except Exception as e:
        print(f"❌ Error checking Tesseract: {str(e)}")
        return False

def check_tesseract_languages():
    """Check available Tesseract languages"""
    print("🌐 Checking Tesseract language support...")
    
    try:
        result = subprocess.run("tesseract --list-langs", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            languages = result.stdout.strip().split('\n')[1:]  # Skip first line
            
            required_langs = ['eng', 'ara']
            available_langs = []
            missing_langs = []
            
            for lang in required_langs:
                if lang in languages:
                    available_langs.append(lang)
                else:
                    missing_langs.append(lang)
            
            if available_langs:
                print(f"✅ Available languages: {', '.join(available_langs)}")
            
            if missing_langs:
                print(f"⚠️ Missing languages: {', '.join(missing_langs)}")
                print("   To install Arabic support:")
                print("   - Download Arabic language data from https://github.com/tesseract-ocr/tessdata")
                print("   - Place ara.traineddata in your Tesseract tessdata directory")
            
            return len(available_langs) > 0
        else:
            print("❌ Could not check Tesseract languages")
            return False
    except Exception as e:
        print(f"❌ Error checking Tesseract languages: {str(e)}")
        return False

def run_unit_tests():
    """Run unit tests"""
    print("🧪 Running unit tests...")
    
    if os.path.exists("tests/test_ocr.py"):
        return run_command("python -m pytest tests/test_ocr.py -v", "Running OCR tests")
    else:
        print("⚠️ Test files not found, skipping unit tests")
        return True

def create_upload_directories():
    """Create necessary upload directories"""
    print("📁 Creating upload directories...")
    
    directories = [
        "uploads",
        "uploads/student_answers",
        "uploads/answer_keys",
        "uploads/reports",
        "uploads/signatures"
    ]
    
    try:
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
        print("✅ Upload directories created")
        return True
    except Exception as e:
        print(f"❌ Failed to create directories: {str(e)}")
        return False

def main():
    """Main setup function"""
    print("🚀 AHcorrecting Backend Setup")
    print("=" * 40)
    
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"📍 Working directory: {script_dir}")
    
    success_count = 0
    total_steps = 8
    
    # Step 1: Check Python version
    if check_python_version():
        success_count += 1
    
    # Step 2: Install dependencies
    if install_dependencies():
        success_count += 1
    
    # Step 3: Check Tesseract
    if check_tesseract():
        success_count += 1
    
    # Step 4: Check Tesseract languages
    if check_tesseract_languages():
        success_count += 1
    
    # Step 5: Create directories
    if create_upload_directories():
        success_count += 1
    
    # Step 6: Setup database
    if setup_database():
        success_count += 1
    
    # Step 7: Create admin user
    if create_admin_user():
        success_count += 1
    
    # Step 8: Run tests
    if run_unit_tests():
        success_count += 1
    
    print("\n" + "=" * 40)
    print(f"📊 Setup Summary: {success_count}/{total_steps} steps completed successfully")
    
    if success_count == total_steps:
        print("🎉 Setup completed successfully!")
        print("\nNext steps:")
        print("1. Start the server: python app.py")
        print("2. Test the workflow: python test_workflow.py")
        print("3. Access admin panel with:")
        print("   Email: <EMAIL>")
        print("   Password: admin123")
    else:
        print("⚠️ Setup completed with some issues.")
        print("Please review the errors above and fix them before proceeding.")
    
    return success_count == total_steps

if __name__ == "__main__":
    main()
