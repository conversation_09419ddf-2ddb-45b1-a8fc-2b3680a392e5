# AHcorrecting API Documentation

Base URL: `http://localhost:5000/api`

## Authentication

All protected endpoints require a JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

### Login
**POST** `/auth/login`

Request:
```json
{
  "email": "<EMAIL>",
  "password": "admin123"
}
```

Response:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": 1,
    "name": "Admin",
    "email": "<EMAIL>",
    "role": "admin"
  }
}
```

### Get Current User
**GET** `/auth/me`

Response:
```json
{
  "id": 1,
  "name": "Admin",
  "email": "<EMAIL>",
  "role": "admin"
}
```

## Admin Management

### Users

#### List Users
**GET** `/admin/users`

Response:
```json
[
  {
    "id": 1,
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "role": "teacher",
    "subjects": ["Mathematics", "Physics"]
  }
]
```

#### Create User
**POST** `/admin/users`

Request:
```json
{
  "name": "Jane Teacher",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "teacher",
  "subjects": ["Mathematics"]
}
```

#### Update User
**PUT** `/admin/users/{id}`

Request:
```json
{
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "subjects": ["Mathematics", "Chemistry"]
}
```

#### Delete User
**DELETE** `/admin/users/{id}`

### Subjects

#### List Subjects
**GET** `/admin/subjects`

Response:
```json
[
  {
    "id": 1,
    "name": "Mathematics",
    "description": "Basic mathematics course"
  }
]
```

#### Create Subject
**POST** `/admin/subjects`

Request:
```json
{
  "name": "Physics",
  "description": "Introduction to Physics"
}
```

## Corrections & Grading

### File Uploads

#### Upload Answer Key
**POST** `/corrections/upload/answer-key`

Form Data:
- `file`: Image or PDF file
- `subject_id`: Subject ID
- `language`: "en" or "ar"

Response:
```json
{
  "message": "Answer key uploaded successfully",
  "file_path": "answer_keys/answer_key_abc123.png",
  "subject_id": "1",
  "language": "en"
}
```

#### Upload Student Answer
**POST** `/corrections/upload/student-answer`

Form Data:
- `file`: Image or PDF file
- `subject_id`: Subject ID
- `student_name`: Student name
- `language`: "en" or "ar"

Response:
```json
{
  "message": "Student answer uploaded successfully",
  "file_path": "student_answers/student_def456.png",
  "student_name": "John Doe",
  "subject_id": "1",
  "language": "en"
}
```

#### Upload Teacher Signature
**POST** `/corrections/upload/signature`

Form Data:
- `file`: Image file

Response:
```json
{
  "message": "Signature uploaded successfully",
  "signature_path": "signatures/signature_1_ghi789.png"
}
```

### Processing

#### Process Single Correction
**POST** `/corrections/process`

Request:
```json
{
  "student_answer_path": "student_answers/student_def456.png",
  "answer_key_path": "answer_keys/answer_key_abc123.png",
  "subject_id": 1,
  "student_name": "John Doe",
  "language": "en",
  "notes": "First attempt"
}
```

Response:
```json
{
  "correction_id": 1,
  "score": 0.85,
  "similarity": 0.85,
  "matches": true,
  "student_text": "The answer is 42",
  "key_text": "The answer is 42",
  "student_name": "John Doe",
  "subject": "Mathematics",
  "date": "2024-01-15T10:30:00",
  "language": "en"
}
```

#### Batch Process Corrections
**POST** `/corrections/batch-process`

Request:
```json
{
  "corrections": [
    {
      "student_answer_path": "student_answers/student1.png",
      "answer_key_path": "answer_keys/key1.png",
      "subject_id": 1,
      "student_name": "Alice",
      "language": "en"
    },
    {
      "student_answer_path": "student_answers/student2.png",
      "answer_key_path": "answer_keys/key1.png",
      "subject_id": 1,
      "student_name": "Bob",
      "language": "en"
    }
  ]
}
```

Response:
```json
{
  "successful_corrections": 2,
  "failed_corrections": 0,
  "results": [
    {
      "index": 0,
      "correction_id": 1,
      "score": 0.9,
      "similarity": 0.9,
      "matches": true,
      "student_name": "Alice",
      "subject": "Mathematics"
    },
    {
      "index": 1,
      "correction_id": 2,
      "score": 0.7,
      "similarity": 0.7,
      "matches": false,
      "student_name": "Bob",
      "subject": "Mathematics"
    }
  ],
  "failures": []
}
```

### Report Generation

#### Generate Single Report
**POST** `/corrections/generate-report/{correction_id}`

Request:
```json
{
  "student_name": "John Doe",
  "signature_path": "signatures/signature_1_ghi789.png"
}
```

Response:
```json
{
  "message": "Report generated successfully",
  "report_path": "reports/correction_report_1_20240115_103000.pdf",
  "download_url": "/api/corrections/download-report/correction_report_1_20240115_103000.pdf"
}
```

#### Generate Batch Report
**POST** `/corrections/generate-batch-report`

Request:
```json
{
  "correction_ids": [1, 2, 3],
  "signature_path": "signatures/signature_1_ghi789.png"
}
```

Response:
```json
{
  "message": "Batch report generated successfully",
  "report_path": "reports/batch_correction_report_20240115_103000.pdf",
  "download_url": "/api/corrections/download-report/batch_correction_report_20240115_103000.pdf",
  "corrections_count": 3
}
```

#### Download Report
**GET** `/corrections/download-report/{filename}`

Returns PDF file for download.

## Error Responses

All endpoints return appropriate HTTP status codes and error messages:

### 400 Bad Request
```json
{
  "error": "Email and password are required"
}
```

### 401 Unauthorized
```json
{
  "error": "Invalid password"
}
```

### 403 Forbidden
```json
{
  "error": "Unauthorized. Only teachers and admins can upload answer keys."
}
```

### 404 Not Found
```json
{
  "error": "Subject not found"
}
```

### 500 Internal Server Error
```json
{
  "error": "OCR processing failed: Tesseract not found"
}
```

## File Upload Specifications

### Supported File Types
- Images: PNG, JPG, JPEG, GIF, TIFF, BMP
- Documents: PDF

### File Size Limits
- Maximum file size: 16MB

### File Processing
- PDFs are automatically converted to images for OCR
- Images are preprocessed for better OCR accuracy
- Files are stored securely with unique names

## Language Support

### English (`"en"`)
- Full OCR support
- Advanced text preprocessing
- Multiple similarity algorithms

### Arabic (`"ar"`)
- OCR support (requires Arabic language data)
- Diacritic removal and normalization
- Character-level similarity matching

## Rate Limiting

Currently no rate limiting is implemented. For production use, consider implementing rate limiting based on:
- Requests per minute per user
- File upload size limits
- Concurrent processing limits

## Testing

Use the provided test script to verify API functionality:
```bash
python test_workflow.py
```

This script tests the complete workflow including file uploads, processing, and report generation.
