#!/usr/bin/env python3
"""
Quick test script to verify the AHcorrecting backend is working
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:5000/api"
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "admin123"

def test_login():
    """Test login functionality"""
    print("🔐 Testing login...")
    
    login_data = {
        "email": ADMIN_EMAIL,
        "password": ADMIN_PASSWORD
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Login successful! User: {data['user']['name']}")
            return data['access_token']
        else:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error: {str(e)}")
        return None

def test_subjects(token):
    """Test subjects endpoint"""
    print("📚 Testing subjects...")
    
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        response = requests.get(f"{BASE_URL}/admin/subjects", headers=headers, timeout=10)
        
        if response.status_code == 200:
            subjects = response.json()
            print(f"✅ Subjects retrieved: {len(subjects)} subjects found")
            return True
        else:
            print(f"❌ Subjects test failed: {response.status_code} - {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error: {str(e)}")
        return False

def test_create_subject(token):
    """Test creating a subject"""
    print("➕ Testing subject creation...")
    
    headers = {'Authorization': f'Bearer {token}'}
    subject_data = {
        "name": "Test Mathematics",
        "description": "Test subject for mathematics grading"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/admin/subjects", json=subject_data, headers=headers, timeout=10)
        
        if response.status_code == 201:
            subject = response.json()
            print(f"✅ Subject created: {subject['name']} (ID: {subject['id']})")
            return subject['id']
        else:
            print(f"❌ Subject creation failed: {response.status_code} - {response.text}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error: {str(e)}")
        return None

def test_server_connection():
    """Test basic server connection"""
    print("🌐 Testing server connection...")
    
    try:
        response = requests.get(f"{BASE_URL}/auth/login", timeout=5)
        # We expect a 405 Method Not Allowed since we're using GET instead of POST
        if response.status_code == 405:
            print("✅ Server is running and responding!")
            return True
        else:
            print(f"⚠️ Unexpected response: {response.status_code}")
            return True  # Server is still responding
    except requests.exceptions.RequestException as e:
        print(f"❌ Server connection failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 AHcorrecting Backend Quick Test")
    print("=" * 40)
    
    # Test 1: Server connection
    if not test_server_connection():
        print("\n💥 Server is not running. Please start the server first:")
        print("   python app.py")
        return False
    
    # Test 2: Login
    token = test_login()
    if not token:
        print("\n💥 Login failed. Cannot continue with other tests.")
        return False
    
    # Test 3: Subjects
    if not test_subjects(token):
        print("\n⚠️ Subjects test failed, but continuing...")
    
    # Test 4: Create subject
    subject_id = test_create_subject(token)
    if subject_id:
        print(f"✅ Test subject created with ID: {subject_id}")
    
    print("\n🎉 Basic tests completed successfully!")
    print("\n📋 Next steps:")
    print("1. The server is running on http://localhost:5000")
    print("2. You can now test file uploads and OCR processing")
    print("3. Admin credentials:")
    print(f"   Email: {ADMIN_EMAIL}")
    print(f"   Password: {ADMIN_PASSWORD}")
    
    return True

if __name__ == "__main__":
    main()
