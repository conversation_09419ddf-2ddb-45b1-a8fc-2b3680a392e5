#!/usr/bin/env python3
"""
Test script for AHcorrecting complete workflow
This script tests the entire grading workflow including:
- File uploads
- OCR processing
- Answer comparison
- PDF report generation
"""

import os
import sys
import requests
import json
from PIL import Image, ImageDraw, ImageFont
import tempfile
import shutil

# Configuration
BASE_URL = "http://localhost:5000/api"
TEST_ADMIN_EMAIL = "<EMAIL>"
TEST_ADMIN_PASSWORD = "admin123"

class WorkflowTester:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        self.temp_dir = tempfile.mkdtemp()
        print(f"Created temporary directory: {self.temp_dir}")
    
    def cleanup(self):
        """Clean up temporary files"""
        shutil.rmtree(self.temp_dir)
        print(f"Cleaned up temporary directory: {self.temp_dir}")
    
    def create_test_image(self, text, filename, width=400, height=200):
        """Create a test image with text"""
        img = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(img)
        
        try:
            font = ImageFont.load_default()
        except:
            font = ImageFont.load_default()
        
        # Calculate text position (center)
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        x = (width - text_width) // 2
        y = (height - text_height) // 2
        
        # Draw text
        draw.text((x, y), text, fill='black', font=font)
        
        # Save image
        image_path = os.path.join(self.temp_dir, filename)
        img.save(image_path)
        
        return image_path
    
    def login(self):
        """Login as admin"""
        print("🔐 Testing login...")
        
        login_data = {
            "email": TEST_ADMIN_EMAIL,
            "password": TEST_ADMIN_PASSWORD
        }
        
        response = self.session.post(f"{BASE_URL}/auth/login", json=login_data)
        
        if response.status_code == 200:
            data = response.json()
            self.token = data['access_token']
            self.session.headers.update({'Authorization': f'Bearer {self.token}'})
            print(f"✅ Login successful! User: {data['user']['name']}")
            return True
        else:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return False
    
    def create_test_subject(self):
        """Create a test subject"""
        print("📚 Creating test subject...")
        
        subject_data = {
            "name": "Test Mathematics",
            "description": "Test subject for mathematics grading"
        }
        
        response = self.session.post(f"{BASE_URL}/admin/subjects", json=subject_data)
        
        if response.status_code == 201:
            data = response.json()
            print(f"✅ Subject created: {data['name']} (ID: {data['id']})")
            return data['id']
        else:
            print(f"❌ Subject creation failed: {response.status_code} - {response.text}")
            return None
    
    def upload_answer_key(self, subject_id):
        """Upload an answer key"""
        print("📤 Uploading answer key...")
        
        # Create answer key image
        answer_key_path = self.create_test_image(
            "The answer is 42", 
            "answer_key.png"
        )
        
        with open(answer_key_path, 'rb') as f:
            files = {'file': f}
            data = {
                'subject_id': subject_id,
                'language': 'en'
            }
            
            response = self.session.post(
                f"{BASE_URL}/corrections/upload/answer-key",
                files=files,
                data=data
            )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Answer key uploaded: {data['file_path']}")
            return data['file_path']
        else:
            print(f"❌ Answer key upload failed: {response.status_code} - {response.text}")
            return None
    
    def upload_student_answer(self, subject_id, student_name, answer_text):
        """Upload a student answer"""
        print(f"📤 Uploading student answer for {student_name}...")
        
        # Create student answer image
        student_answer_path = self.create_test_image(
            answer_text, 
            f"student_{student_name.replace(' ', '_')}.png"
        )
        
        with open(student_answer_path, 'rb') as f:
            files = {'file': f}
            data = {
                'subject_id': subject_id,
                'student_name': student_name,
                'language': 'en'
            }
            
            response = self.session.post(
                f"{BASE_URL}/corrections/upload/student-answer",
                files=files,
                data=data
            )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Student answer uploaded: {data['file_path']}")
            return data['file_path']
        else:
            print(f"❌ Student answer upload failed: {response.status_code} - {response.text}")
            return None
    
    def process_correction(self, student_answer_path, answer_key_path, subject_id, student_name):
        """Process a correction"""
        print(f"⚙️ Processing correction for {student_name}...")
        
        correction_data = {
            "student_answer_path": student_answer_path,
            "answer_key_path": answer_key_path,
            "subject_id": subject_id,
            "student_name": student_name,
            "language": "en",
            "notes": f"Automated test correction for {student_name}"
        }
        
        response = self.session.post(f"{BASE_URL}/corrections/process", json=correction_data)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Correction processed! Score: {data['score']:.2f} ({data['score']*100:.1f}%)")
            print(f"   Similarity: {data['similarity']:.2f}, Matches: {data['matches']}")
            return data['correction_id']
        else:
            print(f"❌ Correction processing failed: {response.status_code} - {response.text}")
            return None
    
    def generate_report(self, correction_id, student_name):
        """Generate a PDF report"""
        print(f"📄 Generating report for correction {correction_id}...")
        
        report_data = {
            "student_name": student_name
        }
        
        response = self.session.post(
            f"{BASE_URL}/corrections/generate-report/{correction_id}",
            json=report_data
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Report generated: {data['report_path']}")
            return data['report_path']
        else:
            print(f"❌ Report generation failed: {response.status_code} - {response.text}")
            return None
    
    def test_batch_processing(self, subject_id, answer_key_path):
        """Test batch processing of multiple students"""
        print("🔄 Testing batch processing...")
        
        students = [
            ("Alice Johnson", "The answer is 42"),
            ("Bob Smith", "The answer is forty-two"),
            ("Carol Davis", "42 is the answer"),
            ("David Wilson", "The solution is 42"),
            ("Eve Brown", "I think it's 24")  # Wrong answer
        ]
        
        corrections_data = []
        
        for student_name, answer_text in students:
            student_answer_path = self.upload_student_answer(subject_id, student_name, answer_text)
            if student_answer_path:
                corrections_data.append({
                    "student_answer_path": student_answer_path,
                    "answer_key_path": answer_key_path,
                    "subject_id": subject_id,
                    "student_name": student_name,
                    "language": "en",
                    "notes": f"Batch test for {student_name}"
                })
        
        if corrections_data:
            batch_data = {"corrections": corrections_data}
            
            response = self.session.post(f"{BASE_URL}/corrections/batch-process", json=batch_data)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Batch processing completed!")
                print(f"   Successful: {data['successful_corrections']}")
                print(f"   Failed: {data['failed_corrections']}")
                
                # Generate batch report
                if data['results']:
                    correction_ids = [result['correction_id'] for result in data['results']]
                    batch_report_data = {"correction_ids": correction_ids}
                    
                    response = self.session.post(
                        f"{BASE_URL}/corrections/generate-batch-report",
                        json=batch_report_data
                    )
                    
                    if response.status_code == 200:
                        report_data = response.json()
                        print(f"✅ Batch report generated: {report_data['report_path']}")
                    else:
                        print(f"❌ Batch report generation failed: {response.status_code}")
                
                return True
            else:
                print(f"❌ Batch processing failed: {response.status_code} - {response.text}")
                return False
        
        return False
    
    def run_complete_test(self):
        """Run the complete workflow test"""
        print("🚀 Starting AHcorrecting Complete Workflow Test")
        print("=" * 50)
        
        try:
            # Step 1: Login
            if not self.login():
                return False
            
            # Step 2: Create test subject
            subject_id = self.create_test_subject()
            if not subject_id:
                return False
            
            # Step 3: Upload answer key
            answer_key_path = self.upload_answer_key(subject_id)
            if not answer_key_path:
                return False
            
            # Step 4: Test single correction
            print("\n📝 Testing single correction...")
            student_answer_path = self.upload_student_answer(
                subject_id, 
                "John Doe", 
                "The answer is 42"
            )
            
            if student_answer_path:
                correction_id = self.process_correction(
                    student_answer_path, 
                    answer_key_path, 
                    subject_id, 
                    "John Doe"
                )
                
                if correction_id:
                    self.generate_report(correction_id, "John Doe")
            
            # Step 5: Test batch processing
            print("\n📊 Testing batch processing...")
            self.test_batch_processing(subject_id, answer_key_path)
            
            print("\n🎉 Complete workflow test finished successfully!")
            return True
            
        except Exception as e:
            print(f"\n💥 Test failed with exception: {str(e)}")
            return False
        
        finally:
            self.cleanup()

def main():
    """Main function"""
    print("AHcorrecting Workflow Tester")
    print("This script tests the complete grading workflow.")
    print("Make sure the Flask server is running on http://localhost:5000")
    print()
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/auth/login", timeout=5)
    except requests.exceptions.RequestException:
        print("❌ Cannot connect to the server. Make sure it's running on http://localhost:5000")
        return
    
    tester = WorkflowTester()
    success = tester.run_complete_test()
    
    if success:
        print("\n✅ All tests passed! The AHcorrecting system is working correctly.")
    else:
        print("\n❌ Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()
