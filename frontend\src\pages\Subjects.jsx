import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext.jsx';
import { adminAPI } from '../services/api.js';
import {
  BookOpenIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  XMarkIcon,
  CheckIcon,
  AcademicCapIcon,
} from '@heroicons/react/24/outline';

const Subjects = () => {
  const { isAdmin } = useAuth();
  const [subjects, setSubjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingSubject, setEditingSubject] = useState(null);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });

  useEffect(() => {
    if (isAdmin) {
      fetchSubjects();
    }
  }, [isAdmin]);

  const fetchSubjects = async () => {
    try {
      const data = await adminAPI.getSubjects();
      setSubjects(data);
    } catch (error) {
      setError('Failed to fetch subjects');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const openModal = (subject = null) => {
    if (subject) {
      setEditingSubject(subject);
      setFormData({
        name: subject.name,
        description: subject.description || ''
      });
    } else {
      setEditingSubject(null);
      setFormData({
        name: '',
        description: ''
      });
    }
    setShowModal(true);
    setError('');
    setSuccess('');
  };

  const closeModal = () => {
    setShowModal(false);
    setEditingSubject(null);
    setFormData({
      name: '',
      description: ''
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    try {
      if (editingSubject) {
        await adminAPI.updateSubject(editingSubject.id, formData);
        setSuccess('Subject updated successfully');
      } else {
        await adminAPI.createSubject(formData);
        setSuccess('Subject created successfully');
      }
      
      fetchSubjects();
      closeModal();
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to save subject');
    }
  };

  const handleDelete = async (subjectId) => {
    if (window.confirm('Are you sure you want to delete this subject?')) {
      try {
        await adminAPI.deleteSubject(subjectId);
        setSuccess('Subject deleted successfully');
        fetchSubjects();
      } catch (error) {
        setError('Failed to delete subject');
      }
    }
  };

  if (!isAdmin) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900">Access Denied</h2>
        <p className="mt-2 text-gray-600">You don't have permission to access this page.</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin mx-auto h-12 w-12 text-primary-600 mb-4">
          <BookOpenIcon />
        </div>
        <p className="text-gray-600">Loading subjects...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="page-header">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="page-title">Subjects Management</h1>
            <p className="mt-2 text-gray-600">
              Manage subjects and courses in the system
            </p>
          </div>
          <button
            onClick={() => openModal()}
            className="btn-primary"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add Subject
          </button>
        </div>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div className="rounded-xl bg-gradient-to-r from-success-50 to-success-100 border border-success-200 p-4">
          <div className="flex">
            <CheckIcon className="h-5 w-5 text-success-400" />
            <div className="ml-3">
              <p className="text-sm text-success-700 font-medium">{success}</p>
            </div>
          </div>
        </div>
      )}

      {error && (
        <div className="rounded-xl bg-gradient-to-r from-danger-50 to-danger-100 border border-danger-200 p-4">
          <div className="flex">
            <XMarkIcon className="h-5 w-5 text-danger-400" />
            <div className="ml-3">
              <p className="text-sm text-danger-700 font-medium">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Subjects Grid */}
      {subjects.length === 0 ? (
        <div className="text-center py-12">
          <BookOpenIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No subjects</h3>
          <p className="mt-1 text-sm text-gray-500">Get started by creating a new subject.</p>
          <div className="mt-6">
            <button
              onClick={() => openModal()}
              className="btn-primary"
            >
              <PlusIcon className="h-5 w-5 mr-2" />
              Add Subject
            </button>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {subjects.map((subject) => (
            <div key={subject.id} className="card group hover:shadow-large transition-all duration-300">
              <div className="flex items-start justify-between">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-primary-600 rounded-xl flex items-center justify-center shadow-medium">
                      <AcademicCapIcon className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <div className="ml-4 flex-1">
                    <h3 className="text-lg font-bold text-gray-900 group-hover:text-primary-600 transition-colors duration-200">
                      {subject.name}
                    </h3>
                    <p className="text-sm text-gray-600 mt-1">
                      {subject.description || 'No description provided'}
                    </p>
                  </div>
                </div>
                <div className="flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <button
                    onClick={() => openModal(subject)}
                    className="p-2 text-primary-600 hover:text-primary-800 hover:bg-primary-50 rounded-lg transition-colors duration-200"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(subject.id)}
                    className="p-2 text-danger-600 hover:text-danger-800 hover:bg-danger-50 rounded-lg transition-colors duration-200"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
              
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span>Subject ID: {subject.id}</span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                    Active
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-2xl bg-white">
            <div className="mt-3">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {editingSubject ? 'Edit Subject' : 'Add New Subject'}
                </h3>
                <button
                  onClick={closeModal}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Subject Name
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="input-field"
                    placeholder="e.g., Mathematics, Physics, Chemistry"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    className="input-field"
                    rows={3}
                    placeholder="Brief description of the subject..."
                  />
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={closeModal}
                    className="btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn-primary"
                  >
                    {editingSubject ? 'Update' : 'Create'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Subjects;
