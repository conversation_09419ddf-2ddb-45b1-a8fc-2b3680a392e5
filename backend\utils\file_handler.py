import os
import uuid
from werkzeug.utils import secure_filename
from PIL import Image
import fitz  # PyMuPDF for PDF handling
import cv2
import numpy as np

class FileHandler:
    """Handle file uploads, validation, and processing"""
    
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'pdf', 'tiff', 'bmp'}
    MAX_FILE_SIZE = 16 * 1024 * 1024  # 16MB
    
    def __init__(self, upload_folder):
        self.upload_folder = upload_folder
    
    def allowed_file(self, filename):
        """Check if file extension is allowed"""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in self.ALLOWED_EXTENSIONS
    
    def validate_file(self, file):
        """Validate uploaded file"""
        if not file or file.filename == '':
            return False, "No file selected"
        
        if not self.allowed_file(file.filename):
            return False, f"File type not allowed. Allowed types: {', '.join(self.ALLOWED_EXTENSIONS)}"
        
        # Check file size (this is approximate, actual size check is done by Flask)
        file.seek(0, 2)  # Seek to end
        size = file.tell()
        file.seek(0)  # Reset to beginning
        
        if size > self.MAX_FILE_SIZE:
            return False, f"File too large. Maximum size: {self.MAX_FILE_SIZE // (1024*1024)}MB"
        
        return True, "File is valid"
    
    def save_file(self, file, subfolder, filename_prefix=None):
        """Save uploaded file to specified subfolder"""
        try:
            # Validate file
            is_valid, message = self.validate_file(file)
            if not is_valid:
                return None
            
            # Generate secure filename
            original_filename = secure_filename(file.filename)
            file_extension = original_filename.rsplit('.', 1)[1].lower()
            
            if filename_prefix:
                filename = f"{filename_prefix}.{file_extension}"
            else:
                filename = f"{uuid.uuid4().hex}.{file_extension}"
            
            # Create full path
            subfolder_path = os.path.join(self.upload_folder, subfolder)
            os.makedirs(subfolder_path, exist_ok=True)
            
            file_path = os.path.join(subfolder_path, filename)
            
            # Save file
            file.save(file_path)
            
            # Return relative path for database storage
            relative_path = os.path.join(subfolder, filename)
            
            # If it's a PDF, convert first page to image for OCR
            if file_extension == 'pdf':
                image_path = self.convert_pdf_to_image(file_path)
                if image_path:
                    return os.path.relpath(image_path, self.upload_folder)
            
            return relative_path
            
        except Exception as e:
            print(f"Error saving file: {str(e)}")
            return None
    
    def convert_pdf_to_image(self, pdf_path):
        """Convert first page of PDF to image for OCR processing"""
        try:
            # Open PDF
            doc = fitz.open(pdf_path)
            
            # Get first page
            page = doc[0]
            
            # Convert to image
            mat = fitz.Matrix(2.0, 2.0)  # 2x zoom for better quality
            pix = page.get_pixmap(matrix=mat)
            
            # Save as PNG
            image_filename = pdf_path.rsplit('.', 1)[0] + '_page1.png'
            pix.save(image_filename)
            
            doc.close()
            
            return image_filename
            
        except Exception as e:
            print(f"Error converting PDF to image: {str(e)}")
            return None
    
    def preprocess_image_for_ocr(self, image_path):
        """Preprocess image for better OCR results"""
        try:
            # Read image
            img = cv2.imread(image_path)
            if img is None:
                return None
            
            # Convert to grayscale
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # Apply adaptive thresholding
            thresh = cv2.adaptiveThreshold(
                gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY, 11, 2
            )
            
            # Noise removal
            denoised = cv2.fastNlMeansDenoising(thresh)
            
            # Save processed image
            processed_filename = image_path.rsplit('.', 1)[0] + '_processed.png'
            cv2.imwrite(processed_filename, denoised)
            
            return processed_filename
            
        except Exception as e:
            print(f"Error preprocessing image: {str(e)}")
            return None
    
    def get_file_info(self, file_path):
        """Get information about uploaded file"""
        try:
            full_path = os.path.join(self.upload_folder, file_path)
            
            if not os.path.exists(full_path):
                return None
            
            stat = os.stat(full_path)
            file_extension = file_path.rsplit('.', 1)[1].lower() if '.' in file_path else ''
            
            info = {
                'path': file_path,
                'size': stat.st_size,
                'created': stat.st_ctime,
                'modified': stat.st_mtime,
                'extension': file_extension,
                'is_image': file_extension in {'png', 'jpg', 'jpeg', 'gif', 'tiff', 'bmp'},
                'is_pdf': file_extension == 'pdf'
            }
            
            # If it's an image, get dimensions
            if info['is_image']:
                try:
                    with Image.open(full_path) as img:
                        info['width'], info['height'] = img.size
                        info['format'] = img.format
                except Exception:
                    pass
            
            return info
            
        except Exception as e:
            print(f"Error getting file info: {str(e)}")
            return None
    
    def delete_file(self, file_path):
        """Delete uploaded file"""
        try:
            full_path = os.path.join(self.upload_folder, file_path)
            
            if os.path.exists(full_path):
                os.remove(full_path)
                
                # Also remove processed versions if they exist
                base_path = full_path.rsplit('.', 1)[0]
                for suffix in ['_page1.png', '_processed.png']:
                    processed_path = base_path + suffix
                    if os.path.exists(processed_path):
                        os.remove(processed_path)
                
                return True
            
            return False
            
        except Exception as e:
            print(f"Error deleting file: {str(e)}")
            return False
    
    def cleanup_old_files(self, days_old=30):
        """Clean up files older than specified days"""
        try:
            import time
            current_time = time.time()
            cutoff_time = current_time - (days_old * 24 * 60 * 60)
            
            deleted_count = 0
            
            for root, dirs, files in os.walk(self.upload_folder):
                for file in files:
                    file_path = os.path.join(root, file)
                    if os.path.getctime(file_path) < cutoff_time:
                        try:
                            os.remove(file_path)
                            deleted_count += 1
                        except Exception:
                            pass
            
            return deleted_count
            
        except Exception as e:
            print(f"Error during cleanup: {str(e)}")
            return 0
