# AHcorrecting Backend

AI-Powered Handwriting Correction Tool - Backend API

## 🚀 Features

- **Multi-language OCR**: Support for English and Arabic text recognition
- **File Upload Management**: Secure handling of images and PDFs
- **AI-Powered Grading**: Advanced text similarity algorithms
- **PDF Report Generation**: Detailed grading reports with teacher signatures
- **Batch Processing**: Grade multiple submissions simultaneously
- **Role-based Access**: Admin, Teacher, and Student roles
- **RESTful API**: Complete API for frontend integration

## 🛠️ Tech Stack

- **Framework**: Flask (Python)
- **Database**: SQLite (easily configurable to MySQL/PostgreSQL)
- **OCR Engine**: Tesseract OCR
- **Image Processing**: OpenCV, Pillow
- **PDF Generation**: ReportLab
- **Authentication**: JWT tokens
- **Text Matching**: FuzzyWuzzy, Levenshtein distance

## 📋 Prerequisites

- Python 3.8 or higher
- Tesseract OCR
- pip (Python package manager)

### Installing Tesseract OCR

**Windows:**
1. Download from [UB-Mannheim Tesseract](https://github.com/UB-Mannheim/tesseract/wiki)
2. Install and add to PATH
3. For Arabic support, download `ara.traineddata` from [tessdata](https://github.com/tesseract-ocr/tessdata)

**macOS:**
```bash
brew install tesseract
brew install tesseract-lang  # For additional languages
```

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install tesseract-ocr
sudo apt install tesseract-ocr-ara  # For Arabic support
```

## 🚀 Quick Start

### 1. Automated Setup (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd backend

# Run automated setup
python setup_and_test.py
```

This script will:
- Check Python version
- Install dependencies
- Verify Tesseract installation
- Create upload directories
- Set up database
- Create admin user
- Run tests

### 2. Manual Setup

```bash
# Install dependencies
pip install -r requirements.txt

# Create upload directories
mkdir -p uploads/{student_answers,answer_keys,reports,signatures}

# Set up database and create admin user
python create_admin.py

# Start the server
python app.py
```

### 3. Test the System

```bash
# Run unit tests
python -m pytest tests/test_ocr.py -v

# Test complete workflow (server must be running)
python test_workflow.py
```

## 📁 Project Structure

```
backend/
├── app.py                 # Main Flask application
├── models.py              # Database models
├── auth.py                # Authentication endpoints
├── admin.py               # Admin management endpoints
├── corrections.py         # Correction processing endpoints
├── extensions.py          # Flask extensions
├── requirements.txt       # Python dependencies
├── setup_and_test.py     # Automated setup script
├── test_workflow.py      # Complete workflow test
├── ocr/
│   └── corrector.py      # OCR and text comparison logic
├── utils/
│   ├── file_handler.py   # File upload and management
│   └── pdf_generator.py  # PDF report generation
├── tests/
│   └── test_ocr.py       # Unit tests
└── uploads/              # File storage (created automatically)
    ├── student_answers/
    ├── answer_keys/
    ├── reports/
    └── signatures/
```

## 🔌 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user info

### Admin Management
- `GET /api/admin/users` - List all users
- `POST /api/admin/users` - Create new user
- `PUT /api/admin/users/{id}` - Update user
- `DELETE /api/admin/users/{id}` - Delete user
- `GET /api/admin/subjects` - List subjects
- `POST /api/admin/subjects` - Create subject
- `PUT /api/admin/subjects/{id}` - Update subject
- `DELETE /api/admin/subjects/{id}` - Delete subject

### Corrections & Grading
- `POST /api/corrections/upload/answer-key` - Upload answer key
- `POST /api/corrections/upload/student-answer` - Upload student answer
- `POST /api/corrections/process` - Process single correction
- `POST /api/corrections/batch-process` - Process multiple corrections
- `POST /api/corrections/generate-report/{id}` - Generate single report
- `POST /api/corrections/generate-batch-report` - Generate batch report
- `GET /api/corrections/download-report/{filename}` - Download report
- `POST /api/corrections/upload/signature` - Upload teacher signature

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the backend directory:

```env
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here
SQLALCHEMY_DATABASE_URI=sqlite:///ahcorrecting.db
```

### File Upload Limits

Default configuration in `app.py`:
- Maximum file size: 16MB
- Allowed extensions: png, jpg, jpeg, gif, pdf, tiff, bmp

## 🧪 Testing

### Unit Tests

```bash
# Run OCR tests
python -m pytest tests/test_ocr.py -v

# Run specific test
python -m pytest tests/test_ocr.py::TestOCRCorrector::test_similarity_calculation_english -v
```

### Integration Tests

```bash
# Test complete workflow (requires running server)
python test_workflow.py
```

### Manual Testing

1. Start the server: `python app.py`
2. Login with admin credentials:
   - Email: `<EMAIL>`
   - Password: `admin123`
3. Use API endpoints with tools like Postman or curl

## 🌐 Language Support

### English
- Full OCR support
- Advanced text preprocessing
- Multiple similarity algorithms

### Arabic
- OCR support (requires Arabic language data)
- Diacritic removal and normalization
- Character-level similarity matching
- Right-to-left text handling

### Adding New Languages

1. Install Tesseract language data
2. Update `AnswerCorrector` class in `ocr/corrector.py`
3. Add language-specific text preprocessing

## 📊 Grading Algorithm

The system uses multiple similarity metrics:

1. **Sequence Matching**: Character-by-character comparison
2. **Fuzzy String Matching**: Handles typos and variations
3. **Word-based Similarity**: Compares individual words
4. **Character-level Similarity**: For Arabic text
5. **Token Sorting**: Order-independent comparison

Final score is a weighted combination of these metrics.

## 🔒 Security Features

- JWT-based authentication
- Role-based access control
- File type validation
- Secure file uploads
- SQL injection protection (SQLAlchemy ORM)
- CORS configuration

## 🚀 Deployment

### Development
```bash
python app.py
```
Server runs on `http://localhost:5000`

### Production
Use a WSGI server like Gunicorn:
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### Docker (Optional)
```dockerfile
FROM python:3.9
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
RUN apt-get update && apt-get install -y tesseract-ocr tesseract-ocr-ara
COPY . .
EXPOSE 5000
CMD ["python", "app.py"]
```

## 🐛 Troubleshooting

### Common Issues

1. **Tesseract not found**
   - Ensure Tesseract is installed and in PATH
   - Check with: `tesseract --version`

2. **Arabic OCR not working**
   - Install Arabic language data: `ara.traineddata`
   - Verify with: `tesseract --list-langs`

3. **File upload fails**
   - Check file size (max 16MB)
   - Verify file extension is allowed
   - Ensure upload directories exist

4. **Database errors**
   - Delete `instance/ahcorrecting.db` and restart
   - Run `python create_admin.py` again

5. **Import errors**
   - Ensure all dependencies are installed: `pip install -r requirements.txt`
   - Check Python version: `python --version`

### Debug Mode

Enable debug logging by setting `debug=True` in `app.py`:
```python
app.run(debug=True, port=5000, host='0.0.0.0')
```

## 📈 Performance Optimization

- Use Redis for caching (future enhancement)
- Implement async processing for batch operations
- Optimize image preprocessing pipeline
- Add database indexing for large datasets

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Run the diagnostic script: `python setup_and_test.py`
3. Review server logs for error details
4. Create an issue with detailed error information
