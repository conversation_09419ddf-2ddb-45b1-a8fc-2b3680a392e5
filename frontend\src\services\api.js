import axios from 'axios';

// API Configuration
const API_BASE_URL = 'http://localhost:5000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: async (email, password) => {
    const response = await api.post('/auth/login', { email, password });
    return response.data;
  },

  getCurrentUser: async () => {
    const response = await api.get('/auth/me');
    return response.data;
  },

  logout: () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  },
};

// Admin API
export const adminAPI = {
  getUsers: async () => {
    const response = await api.get('/admin/users');
    return response.data;
  },

  createUser: async (userData) => {
    const response = await api.post('/admin/users', userData);
    return response.data;
  },

  updateUser: async (id, userData) => {
    const response = await api.put(`/admin/users/${id}`, userData);
    return response.data;
  },

  deleteUser: async (id) => {
    await api.delete(`/admin/users/${id}`);
  },

  getSubjects: async () => {
    const response = await api.get('/admin/subjects');
    return response.data;
  },

  createSubject: async (subjectData) => {
    const response = await api.post('/admin/subjects', subjectData);
    return response.data;
  },

  updateSubject: async (id, subjectData) => {
    const response = await api.put(`/admin/subjects/${id}`, subjectData);
    return response.data;
  },

  deleteSubject: async (id) => {
    await api.delete(`/admin/subjects/${id}`);
  },

  getDebugInfo: async () => {
    const response = await api.get('/admin/debug/db');
    return response.data;
  },

  getSystemStatus: async () => {
    const response = await api.get('/admin/system/status');
    return response.data;
  },

  getStudents: async () => {
    const response = await api.get('/admin/users');
    return response.data.filter(user => user.role === 'student');
  },
};

// Corrections API
export const correctionsAPI = {
  uploadAnswerKey: async (file, subjectId, language = 'en') => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('subject_id', subjectId.toString());
    formData.append('language', language);

    const response = await api.post('/corrections/upload/answer-key', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  uploadStudentAnswer: async (
    file,
    subjectId,
    studentName,
    language = 'en'
  ) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('subject_id', subjectId.toString());
    formData.append('student_name', studentName);
    formData.append('language', language);

    const response = await api.post('/corrections/upload/student-answer', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  uploadSignature: async (file) => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/corrections/upload/signature', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  processCorrection: async (data) => {
    const response = await api.post('/corrections/process', data);
    return response.data;
  },

  batchProcess: async (data) => {
    const response = await api.post('/corrections/batch-process', data);
    return response.data;
  },

  generateReport: async (correctionId, studentName, signaturePath) => {
    const response = await api.post(`/corrections/generate-report/${correctionId}`, {
      student_name: studentName,
      signature_path: signaturePath,
    });
    return response.data;
  },

  generateBatchReport: async (correctionIds, signaturePath) => {
    const response = await api.post('/corrections/generate-batch-report', {
      correction_ids: correctionIds,
      signature_path: signaturePath,
    });
    return response.data;
  },

  downloadReport: async (filename) => {
    const response = await api.get(`/corrections/download-report/${filename}`, {
      responseType: 'blob',
    });
    return response.data;
  },

  saveGrade: async (gradeData) => {
    const response = await api.post('/corrections/save-grade', gradeData);
    return response.data;
  },

  getStudentGrades: async (studentId) => {
    const response = await api.get(`/corrections/student-grades/${studentId}`);
    return response.data;
  },
};

export default api;
