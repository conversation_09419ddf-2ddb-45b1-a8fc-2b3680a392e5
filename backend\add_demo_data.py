#!/usr/bin/env python3
"""
Add demo accounts and sample data to the database
This script will create:
- Admin account
- Teacher accounts
- Student accounts
- Sample subjects
- Sample enrollments
"""

from app import create_app
from extensions import db
from models import User, Admin, Subject, Enrollment
from werkzeug.security import generate_password_hash

def add_demo_data():
    """Add demo accounts and sample data"""

    app = create_app()
    with app.app_context():
        try:
            print("🚀 Adding demo accounts and sample data...")
            
            # Create Admin Account
            print("\n👑 Creating Admin Account...")
            existing_admin = Admin.query.filter_by(email='<EMAIL>').first()
            if not existing_admin:
                admin = Admin(
                    name='Demo Admin',
                    email='<EMAIL>',
                    password_hash=generate_password_hash('admin123')
                )
                db.session.add(admin)
                print("✅ Created admin account: <EMAIL> / admin123")
            else:
                print("✅ Admin account already exists")
            
            # Create Teacher Accounts
            print("\n👨‍🏫 Creating Teacher Accounts...")
            teachers_data = [
                {'name': 'Dr. <PERSON>', 'email': '<EMAIL>', 'password': 'teacher123'},
                {'name': 'Prof<PERSON> <PERSON>', 'email': '<EMAIL>', 'password': 'teacher123'},
                {'name': 'Ms. <PERSON> <PERSON>', 'email': '<EMAIL>', 'password': 'teacher123'}
            ]
            
            teachers = []
            for teacher_data in teachers_data:
                existing_teacher = User.query.filter_by(email=teacher_data['email']).first()
                if not existing_teacher:
                    teacher = User(
                        name=teacher_data['name'],
                        email=teacher_data['email'],
                        password_hash=generate_password_hash(teacher_data['password']),
                        role='teacher'
                    )
                    db.session.add(teacher)
                    teachers.append(teacher)
                    print(f"✅ Created teacher: {teacher_data['email']} / {teacher_data['password']}")
                else:
                    teachers.append(existing_teacher)
                    print(f"✅ Teacher already exists: {teacher_data['email']}")
            
            # Create Student Accounts
            print("\n👨‍🎓 Creating Student Accounts...")
            students_data = [
                {'name': 'Alice Smith', 'email': '<EMAIL>', 'password': 'student123'},
                {'name': 'Bob Wilson', 'email': '<EMAIL>', 'password': 'student123'},
                {'name': 'Carol Brown', 'email': '<EMAIL>', 'password': 'student123'},
                {'name': 'David Lee', 'email': '<EMAIL>', 'password': 'student123'},
                {'name': 'Emma Garcia', 'email': '<EMAIL>', 'password': 'student123'}
            ]
            
            students = []
            for student_data in students_data:
                existing_student = User.query.filter_by(email=student_data['email']).first()
                if not existing_student:
                    student = User(
                        name=student_data['name'],
                        email=student_data['email'],
                        password_hash=generate_password_hash(student_data['password']),
                        role='student'
                    )
                    db.session.add(student)
                    students.append(student)
                    print(f"✅ Created student: {student_data['email']} / {student_data['password']}")
                else:
                    students.append(existing_student)
                    print(f"✅ Student already exists: {student_data['email']}")
            
            # Commit users first
            db.session.commit()
            
            # Create Sample Subjects
            print("\n📚 Creating Sample Subjects...")
            subjects_data = [
                {'name': 'Mathematics', 'code': 'MATH101', 'description': 'Basic Mathematics and Algebra'},
                {'name': 'English Literature', 'code': 'ENG201', 'description': 'English Language and Literature'},
                {'name': 'Physics', 'code': 'PHY101', 'description': 'Introduction to Physics'},
                {'name': 'Chemistry', 'code': 'CHEM101', 'description': 'General Chemistry'},
                {'name': 'History', 'code': 'HIST101', 'description': 'World History and Civilizations'},
                {'name': 'Biology', 'code': 'BIO101', 'description': 'Introduction to Biology'}
            ]
            
            subjects = []
            for subject_data in subjects_data:
                existing_subject = Subject.query.filter_by(name=subject_data['name']).first()
                if not existing_subject:
                    subject = Subject(
                        name=subject_data['name'],
                        code=subject_data['code'],
                        description=subject_data['description']
                    )
                    db.session.add(subject)
                    subjects.append(subject)
                    print(f"✅ Created subject: {subject_data['name']} ({subject_data['code']})")
                else:
                    subjects.append(existing_subject)
                    print(f"✅ Subject already exists: {subject_data['name']}")
            
            # Commit subjects
            db.session.commit()
            
            # Refresh objects to get IDs
            teachers = User.query.filter_by(role='teacher').all()
            students = User.query.filter_by(role='student').all()
            subjects = Subject.query.all()
            
            # Create Sample Enrollments
            print("\n📝 Creating Sample Enrollments...")
            enrollment_count = 0
            
            # Enroll each student in 2-3 random subjects
            import random
            for student in students:
                # Each student gets enrolled in 2-3 subjects
                num_subjects = random.randint(2, 3)
                student_subjects = random.sample(subjects, num_subjects)
                
                for subject in student_subjects:
                    # Assign a random teacher for each enrollment
                    teacher = random.choice(teachers)
                    
                    # Check if enrollment already exists
                    existing_enrollment = Enrollment.query.filter_by(
                        student_id=student.id,
                        subject_id=subject.id
                    ).first()
                    
                    if not existing_enrollment:
                        enrollment = Enrollment(
                            student_id=student.id,
                            subject_id=subject.id,
                            teacher_id=teacher.id,
                            is_active=True
                        )
                        db.session.add(enrollment)
                        enrollment_count += 1
                        print(f"✅ Enrolled {student.name} in {subject.name} (Teacher: {teacher.name})")
            
            # Commit enrollments
            db.session.commit()
            
            print(f"\n🎉 Demo data added successfully!")
            print(f"📊 Summary:")
            print(f"   - 1 Admin account")
            print(f"   - {len(teachers)} Teacher accounts")
            print(f"   - {len(students)} Student accounts")
            print(f"   - {len(subjects)} Subjects")
            print(f"   - {enrollment_count} Enrollments")
            
            print(f"\n🔑 Login Credentials:")
            print(f"   Admin: <EMAIL> / admin123")
            print(f"   Teachers: <EMAIL>, <EMAIL>, <EMAIL> / teacher123")
            print(f"   Students: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL> / student123")
            
            return True
            
        except Exception as e:
            print(f"❌ Error adding demo data: {e}")
            db.session.rollback()
            return False

if __name__ == "__main__":
    success = add_demo_data()
    
    if success:
        print("\n✅ Demo data setup completed!")
        print("\nYou can now:")
        print("- Login as admin to manage the system")
        print("- Login as a teacher to grade papers")
        print("- Login as a student to view grades and enrolled subjects")
        print("- Test the new enrollment features")
    else:
        print("\n❌ Demo data setup failed!")
        print("Please check the error messages above and try again.")
