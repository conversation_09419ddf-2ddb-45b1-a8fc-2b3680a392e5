#!/usr/bin/env python3
"""
Migration script to create the Grade table
Run this script to add the Grade table to your existing database
"""

from app import app, db
from models import Grade

def create_grade_table():
    """Create the Grade table if it doesn't exist"""
    with app.app_context():
        try:
            # Create the Grade table
            db.create_all()
            print("✅ Grade table created successfully!")
            
            # Verify the table was created
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            
            if 'grade' in tables:
                print("✅ Grade table verified in database")
                
                # Show table structure
                columns = inspector.get_columns('grade')
                print("\n📋 Grade table structure:")
                for col in columns:
                    print(f"  - {col['name']}: {col['type']}")
            else:
                print("❌ Grade table not found in database")
                
        except Exception as e:
            print(f"❌ Error creating Grade table: {e}")
            return False
            
    return True

if __name__ == "__main__":
    print("🚀 Creating Grade table...")
    success = create_grade_table()
    
    if success:
        print("\n✅ Migration completed successfully!")
        print("\nThe Grade table is now ready to store student grades.")
        print("You can now use the Grade Papers interface with student dropdowns.")
    else:
        print("\n❌ Migration failed!")
        print("Please check the error messages above and try again.")
