#!/usr/bin/env python3
"""
Migration script to create the Grade table
Run this script to add the Grade table to your existing database
"""

from app import app, db
from models import Grade, Enrollment

def create_grade_table():
    """Create the Grade and Enrollment tables if they don't exist"""
    with app.app_context():
        try:
            # Create all tables (Grade and Enrollment)
            db.create_all()
            print("✅ Database tables created successfully!")

            # Verify the tables were created
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()

            # Check Grade table
            if 'grade' in tables:
                print("✅ Grade table verified in database")
                columns = inspector.get_columns('grade')
                print("\n📋 Grade table structure:")
                for col in columns:
                    print(f"  - {col['name']}: {col['type']}")
            else:
                print("❌ Grade table not found in database")

            # Check Enrollment table
            if 'enrollment' in tables:
                print("\n✅ Enrollment table verified in database")
                columns = inspector.get_columns('enrollment')
                print("\n📋 Enrollment table structure:")
                for col in columns:
                    print(f"  - {col['name']}: {col['type']}")
            else:
                print("❌ Enrollment table not found in database")

        except Exception as e:
            print(f"❌ Error creating tables: {e}")
            return False

    return True

if __name__ == "__main__":
    print("🚀 Creating Grade and Enrollment tables...")
    success = create_grade_table()

    if success:
        print("\n✅ Migration completed successfully!")
        print("\nThe Grade and Enrollment tables are now ready!")
        print("- Grade table: Store student grades from grading sessions")
        print("- Enrollment table: Track which subjects students are enrolled in")
        print("\nYou can now:")
        print("- Use the Grade Papers interface with student dropdowns")
        print("- View enrolled subjects in the Student Dashboard")
        print("- Enroll students in subjects through the admin interface")
    else:
        print("\n❌ Migration failed!")
        print("Please check the error messages above and try again.")
