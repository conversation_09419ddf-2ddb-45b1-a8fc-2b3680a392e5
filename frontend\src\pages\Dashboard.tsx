import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { adminAPI } from '../services/api';
import {
  UserGroupIcon,
  BookOpenIcon,
  DocumentTextIcon,
  ChartBarIcon,
  AcademicCapIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';

interface DashboardStats {
  students: number;
  teachers: number;
  subjects: number;
  admins: number;
}

const Dashboard: React.FC = () => {
  const { user, isAdmin } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    students: 0,
    teachers: 0,
    subjects: 0,
    admins: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      if (isAdmin) {
        try {
          const debugInfo = await adminAPI.getDebugInfo();
          setStats({
            students: debugInfo.students?.length || 0,
            teachers: debugInfo.teachers?.length || 0,
            subjects: debugInfo.subjects?.length || 0,
            admins: debugInfo.admins?.length || 0,
          });
        } catch (error) {
          console.error('Error fetching stats:', error);
        }
      }
      setLoading(false);
    };

    fetchStats();
  }, [isAdmin]);

  const quickActions = [
    {
      name: 'Grade Papers',
      description: 'Upload and grade student answers',
      href: '/grade',
      icon: DocumentTextIcon,
      color: 'bg-blue-500',
      show: user?.role === 'teacher' || user?.role === 'admin',
    },
    {
      name: 'Batch Grading',
      description: 'Process multiple submissions at once',
      href: '/batch',
      icon: ChartBarIcon,
      color: 'bg-green-500',
      show: user?.role === 'teacher' || user?.role === 'admin',
    },
    {
      name: 'Manage Users',
      description: 'Add and manage teachers and students',
      href: '/users',
      icon: UserGroupIcon,
      color: 'bg-purple-500',
      show: user?.role === 'admin',
    },
    {
      name: 'Manage Subjects',
      description: 'Create and organize subjects',
      href: '/subjects',
      icon: BookOpenIcon,
      color: 'bg-orange-500',
      show: user?.role === 'admin',
    },
  ].filter(action => action.show);

  const statCards = [
    {
      name: 'Total Students',
      value: stats.students,
      icon: AcademicCapIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      show: isAdmin,
    },
    {
      name: 'Total Teachers',
      value: stats.teachers,
      icon: UserGroupIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      show: isAdmin,
    },
    {
      name: 'Total Subjects',
      value: stats.subjects,
      icon: BookOpenIcon,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      show: isAdmin,
    },
    {
      name: 'Total Admins',
      value: stats.admins,
      icon: UserGroupIcon,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      show: isAdmin,
    },
  ].filter(card => card.show);

  const features = [
    {
      name: 'AI-Powered OCR',
      description: 'Advanced text recognition for English and Arabic handwriting',
      icon: AcademicCapIcon,
    },
    {
      name: 'Smart Grading',
      description: 'Intelligent similarity matching and scoring algorithms',
      icon: ChartBarIcon,
    },
    {
      name: 'PDF Reports',
      description: 'Professional grading reports with teacher signatures',
      icon: DocumentTextIcon,
    },
    {
      name: 'Batch Processing',
      description: 'Grade multiple submissions simultaneously for efficiency',
      icon: ClockIcon,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="page-header">
        <h1 className="page-title">
          Welcome back, {user?.name}!
        </h1>
        <p className="mt-2 text-sm text-gray-600">
          Here's what's happening with your AHcorrecting system today.
        </p>
      </div>

      {/* Stats Cards */}
      {isAdmin && (
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {statCards.map((card) => (
            <div key={card.name} className="card">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`p-3 rounded-lg ${card.bgColor}`}>
                    <card.icon className={`h-6 w-6 ${card.color}`} />
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">{card.name}</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {loading ? '...' : card.value}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Quick Actions */}
      <div>
        <h2 className="section-title">Quick Actions</h2>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          {quickActions.map((action) => (
            <a
              key={action.name}
              href={action.href}
              className="card hover:shadow-md transition-shadow duration-200 group"
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`p-3 rounded-lg ${action.color}`}>
                    <action.icon className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-medium text-gray-900 group-hover:text-primary-600">
                    {action.name}
                  </h3>
                  <p className="text-sm text-gray-500">{action.description}</p>
                </div>
              </div>
            </a>
          ))}
        </div>
      </div>

      {/* Features Overview */}
      <div>
        <h2 className="section-title">System Features</h2>
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {features.map((feature) => (
            <div key={feature.name} className="card text-center">
              <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100">
                <feature.icon className="h-6 w-6 text-primary-600" />
              </div>
              <h3 className="mt-4 text-lg font-medium text-gray-900">{feature.name}</h3>
              <p className="mt-2 text-sm text-gray-500">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Getting Started */}
      {user?.role === 'teacher' && (
        <div className="card bg-primary-50 border-primary-200">
          <div className="flex">
            <div className="flex-shrink-0">
              <AcademicCapIcon className="h-6 w-6 text-primary-600" />
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-medium text-primary-900">
                Ready to start grading?
              </h3>
              <p className="mt-2 text-sm text-primary-700">
                Upload your answer key and student submissions to begin the AI-powered grading process.
              </p>
              <div className="mt-4">
                <a
                  href="/grade"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
                >
                  Start Grading
                </a>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* System Status */}
      <div className="card">
        <h3 className="section-title">System Status</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Backend API</span>
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              Connected
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Database</span>
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              Online
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">File Upload</span>
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              Ready
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">OCR Engine</span>
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
              Requires Tesseract
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
