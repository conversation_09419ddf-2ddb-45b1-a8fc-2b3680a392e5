import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext.jsx';
import { correctionsAPI } from '../services/api.js';
import {
  AcademicCapIcon,
  ChartBarIcon,
  CalendarIcon,
  TrophyIcon,
  CheckCircleIcon,
  XCircleIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline';

const StudentDashboard = () => {
  const { user } = useAuth();
  const [grades, setGrades] = useState([]);
  const [enrollments, setEnrollments] = useState([]);
  const [stats, setStats] = useState({
    totalTests: 0,
    averageScore: 0,
    passedTests: 0,
    failedTests: 0
  });

  useEffect(() => {
    if (user?.id) {
      loadStudentGrades();
      loadStudentEnrollments();
    }
  }, [user]);

  const loadStudentGrades = async () => {
    try {
      const response = await correctionsAPI.getStudentGrades(user.id);
      const studentGrades = response.grades;

      setGrades(studentGrades);

      // Calculate statistics
      if (studentGrades.length > 0) {
        const totalScore = studentGrades.reduce((sum, grade) => sum + grade.score, 0);
        const averageScore = totalScore / studentGrades.length;
        const passedTests = studentGrades.filter(grade => grade.matches).length;
        const failedTests = studentGrades.length - passedTests;

        setStats({
          totalTests: studentGrades.length,
          averageScore: averageScore,
          passedTests: passedTests,
          failedTests: failedTests
        });
      }
    } catch (error) {
      console.error('Error loading grades:', error);
      // Fallback to localStorage for backward compatibility
      loadGradesFromLocalStorage();
    }
  };

  const loadGradesFromLocalStorage = () => {
    // Fallback method using localStorage
    const allGrades = JSON.parse(localStorage.getItem('gradedStudents') || '[]');
    const studentGrades = allGrades.filter(grade =>
      grade.studentName && grade.studentName.toLowerCase() === user?.name?.toLowerCase()
    );

    setGrades(studentGrades);

    if (studentGrades.length > 0) {
      const totalScore = studentGrades.reduce((sum, grade) => sum + grade.similarity, 0);
      const averageScore = totalScore / studentGrades.length;
      const passedTests = studentGrades.filter(grade => grade.matches).length;
      const failedTests = studentGrades.length - passedTests;

      setStats({
        totalTests: studentGrades.length,
        averageScore: averageScore,
        passedTests: passedTests,
        failedTests: failedTests
      });
    }
  };

  const loadStudentEnrollments = async () => {
    try {
      const response = await correctionsAPI.getStudentEnrollments(user.id);
      setEnrollments(response.enrollments);
    } catch (error) {
      console.error('Error loading enrollments:', error);
      setEnrollments([]);
    }
  };

  const getScoreColor = (score) => {
    if (score >= 0.8) return 'text-success-600';
    if (score >= 0.6) return 'text-warning-600';
    return 'text-danger-600';
  };

  const getScoreBadge = (score) => {
    if (score >= 0.8) return 'status-badge-success';
    if (score >= 0.6) return 'status-badge-warning';
    return 'status-badge-danger';
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="page-header">
        <h1 className="page-title">My Grades</h1>
        <p className="mt-2 text-gray-600">
          Welcome back, {user?.name}! Here are your test results and performance statistics.
        </p>
      </div>

      {/* Statistics Cards */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-content">
            <div className="stat-icon-container" style={{ background: 'linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%)' }}>
              <DocumentTextIcon className="stat-icon text-white" />
            </div>
            <div className="stat-details">
              <div className="stat-label">Total Tests</div>
              <div className="stat-value">{stats.totalTests}</div>
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-content">
            <div className="stat-icon-container" style={{ background: 'linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%)' }}>
              <ChartBarIcon className="stat-icon text-white" />
            </div>
            <div className="stat-details">
              <div className="stat-label">Average Score</div>
              <div className="stat-value">{Math.round(stats.averageScore * 100)}%</div>
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-content">
            <div className="stat-icon-container" style={{ background: 'linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%)' }}>
              <CheckCircleIcon className="stat-icon text-white" />
            </div>
            <div className="stat-details">
              <div className="stat-label">Passed Tests</div>
              <div className="stat-value">{stats.passedTests}</div>
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-content">
            <div className="stat-icon-container" style={{ background: 'linear-gradient(135deg, var(--danger-500) 0%, var(--danger-600) 100%)' }}>
              <XCircleIcon className="stat-icon text-white" />
            </div>
            <div className="stat-details">
              <div className="stat-label">Failed Tests</div>
              <div className="stat-value">{stats.failedTests}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Overview */}
      {stats.totalTests > 0 && (
        <div className="card">
          <h2 className="section-title">Performance Overview</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 mb-2">
                {Math.round((stats.passedTests / stats.totalTests) * 100)}%
              </div>
              <div className="text-sm text-gray-600">Pass Rate</div>
            </div>
            
            <div className="text-center">
              <div className={`text-2xl font-bold mb-2 ${getScoreColor(stats.averageScore)}`}>
                {Math.round(stats.averageScore * 100)}%
              </div>
              <div className="text-sm text-gray-600">Average Score</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-primary-600 mb-2">
                {stats.totalTests}
              </div>
              <div className="text-sm text-gray-600">Total Attempts</div>
            </div>
          </div>
        </div>
      )}

      {/* Enrolled Subjects */}
      <div className="card">
        <h2 className="section-title">
          <span className="gradient-text">My Subjects</span>
        </h2>
        {enrollments.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {enrollments.map((enrollment) => (
              <div key={enrollment.id} className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center">
                    <AcademicCapIcon className="h-6 w-6 text-blue-600 mr-2" />
                    <div>
                      <h3 className="font-semibold text-gray-900">{enrollment.subject_name}</h3>
                      <p className="text-sm text-gray-600">{enrollment.subject_code}</p>
                    </div>
                  </div>
                  <span className="status-badge-success text-xs">
                    Enrolled
                  </span>
                </div>
                <div className="border-t border-blue-200 pt-3">
                  <div className="flex items-center text-sm text-gray-600">
                    <DocumentTextIcon className="h-4 w-4 mr-1" />
                    <span>Teacher: {enrollment.teacher_name}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-500 mt-1">
                    <CalendarIcon className="h-4 w-4 mr-1" />
                    <span>Enrolled: {new Date(enrollment.enrolled_at).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <AcademicCapIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Subjects Enrolled</h3>
            <p className="text-gray-600">
              You are not enrolled in any subjects yet. Please contact your teacher or administrator to get enrolled in subjects.
            </p>
          </div>
        )}
      </div>

      {/* Recent Grades */}
      {grades.length > 0 ? (
        <div className="card">
          <div className="flex items-center justify-between mb-6">
            <h2 className="section-title">Recent Test Results</h2>
            <button
              onClick={() => {
                // Generate student report
                const csvContent = "data:text/csv;charset=utf-8," 
                  + "Subject,Score,Result,Date\n"
                  + grades.map(grade => 
                      `${grade.subject},${Math.round(grade.similarity * 100)}%,${grade.matches ? 'PASS' : 'FAIL'},${new Date(grade.date).toLocaleDateString()}`
                    ).join("\n");
                
                const encodedUri = encodeURI(csvContent);
                const link = document.createElement("a");
                link.setAttribute("href", encodedUri);
                link.setAttribute("download", `my_grades_${new Date().toISOString().split('T')[0]}.csv`);
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              }}
              className="btn-primary"
            >
              Download My Grades
            </button>
          </div>
          
          <div className="overflow-x-auto">
            <table className="table">
              <thead>
                <tr>
                  <th>Subject</th>
                  <th>Score</th>
                  <th>Result</th>
                  <th>Date</th>
                  <th>Performance</th>
                </tr>
              </thead>
              <tbody>
                {grades.slice().reverse().map((grade, index) => (
                  <tr key={grade.id || index}>
                    <td className="font-medium">{grade.subject}</td>
                    <td>
                      <span className={getScoreColor(grade.score || grade.similarity)}>
                        {Math.round((grade.score || grade.similarity) * 100)}%
                      </span>
                    </td>
                    <td>
                      <span className={grade.matches ? 'status-badge-success' : 'status-badge-danger'}>
                        {grade.matches ? 'PASS' : 'FAIL'}
                      </span>
                    </td>
                    <td>{new Date(grade.date || grade.created_at).toLocaleDateString()}</td>
                    <td>
                      <div className="flex items-center">
                        {grade.matches ? (
                          <CheckCircleIcon className="h-5 w-5 text-success-600 mr-2" />
                        ) : (
                          <XCircleIcon className="h-5 w-5 text-danger-600 mr-2" />
                        )}
                        <span className={getScoreColor(grade.score || grade.similarity)}>
                          {(grade.score || grade.similarity) >= 0.8 ? 'Excellent' :
                           (grade.score || grade.similarity) >= 0.6 ? 'Good' : 'Needs Improvement'}
                        </span>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        <div className="card text-center">
          <div className="py-12">
            <AcademicCapIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Test Results Yet</h3>
            <p className="text-gray-600 mb-6">
              You haven't taken any tests yet. Your grades will appear here once your teacher grades your submissions.
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
              <p className="text-sm text-blue-700">
                <strong>How it works:</strong> When your teacher uploads and grades your answer sheets, 
                your results will automatically appear in this dashboard.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Study Tips */}
      {stats.averageScore < 0.7 && stats.totalTests > 0 && (
        <div className="card">
          <h2 className="section-title">Study Tips</h2>
          <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
            <div className="flex">
              <TrophyIcon className="h-5 w-5 text-yellow-400 mr-3 mt-0.5" />
              <div>
                <h3 className="text-sm font-medium text-yellow-800 mb-2">Improve Your Performance</h3>
                <ul className="text-sm text-yellow-700 space-y-1">
                  <li>• Review your incorrect answers and understand the mistakes</li>
                  <li>• Practice more problems in subjects where you scored lower</li>
                  <li>• Ask your teacher for additional help if needed</li>
                  <li>• Make sure your handwriting is clear and legible</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StudentDashboard;
