import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext.jsx';
import { adminAPI, correctionsAPI } from '../services/api.js';
import {
  DocumentArrowUpIcon,
  ChartBarIcon,
  CheckCircleIcon,
  XCircleIcon,
  DocumentTextIcon,
  PhotoIcon,
  ClockIcon,
  FolderIcon,
  TrashIcon,
} from '@heroicons/react/24/outline';

const BatchGrading = () => {
  const { user } = useAuth();
  const [subjects, setSubjects] = useState([]);
  const [selectedSubject, setSelectedSubject] = useState('');
  const [answerKeyFile, setAnswerKeyFile] = useState(null);
  const [studentFiles, setStudentFiles] = useState([]);
  const [language, setLanguage] = useState('en');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState([]);
  const [error, setError] = useState('');
  const [step, setStep] = useState(1);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    fetchSubjects();
  }, []);

  const fetchSubjects = async () => {
    try {
      const data = await adminAPI.getSubjects();
      setSubjects(data);
    } catch (error) {
      console.error('Error fetching subjects:', error);
    }
  };

  const handleAnswerKeyChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setAnswerKeyFile(file);
    }
  };

  const handleStudentFilesChange = (e) => {
    const files = Array.from(e.target.files);
    setStudentFiles(files);
  };

  const removeStudentFile = (index) => {
    setStudentFiles(prev => prev.filter((_, i) => i !== index));
  };

  const uploadAnswerKey = async () => {
    if (!answerKeyFile || !selectedSubject) return null;
    
    try {
      const response = await correctionsAPI.uploadAnswerKey(
        answerKeyFile,
        selectedSubject,
        language
      );
      return response.file_path;
    } catch (error) {
      throw new Error('Failed to upload answer key');
    }
  };

  const uploadStudentAnswers = async () => {
    const uploadedFiles = [];
    
    for (let i = 0; i < studentFiles.length; i++) {
      const file = studentFiles[i];
      const studentName = file.name.split('.')[0]; // Use filename as student name
      
      try {
        const response = await correctionsAPI.uploadStudentAnswer(
          file,
          selectedSubject,
          studentName,
          language
        );
        uploadedFiles.push({
          path: response.file_path,
          name: studentName,
          originalFile: file
        });
        
        // Update progress
        setProgress(((i + 1) / studentFiles.length) * 50); // First 50% for uploads
      } catch (error) {
        console.error(`Failed to upload ${file.name}:`, error);
      }
    }
    
    return uploadedFiles;
  };

  const processBatchGrading = async () => {
    setLoading(true);
    setError('');
    setProgress(0);
    
    try {
      // Upload answer key
      const answerKeyPath = await uploadAnswerKey();
      if (!answerKeyPath) {
        throw new Error('Failed to upload answer key');
      }

      // Upload student answers
      const uploadedStudentFiles = await uploadStudentAnswers();
      if (uploadedStudentFiles.length === 0) {
        throw new Error('No student files were uploaded successfully');
      }

      // Process each correction
      const correctionResults = [];
      for (let i = 0; i < uploadedStudentFiles.length; i++) {
        const studentFile = uploadedStudentFiles[i];
        
        try {
          const correctionData = {
            student_answer_path: studentFile.path,
            answer_key_path: answerKeyPath,
            subject_id: parseInt(selectedSubject),
            student_name: studentFile.name,
            language: language,
            notes: `Batch processing - ${new Date().toISOString()}`
          };

          const result = await correctionsAPI.processCorrection(correctionData);
          correctionResults.push({
            ...result,
            student_name: studentFile.name,
            originalFile: studentFile.originalFile
          });
          
          // Update progress (50% for uploads + 50% for processing)
          setProgress(50 + ((i + 1) / uploadedStudentFiles.length) * 50);
        } catch (error) {
          console.error(`Failed to process ${studentFile.name}:`, error);
          correctionResults.push({
            student_name: studentFile.name,
            error: error.message,
            originalFile: studentFile.originalFile
          });
        }
      }

      setResults(correctionResults);
      setStep(3);
    } catch (error) {
      setError(error.message || 'Failed to process batch grading');
    } finally {
      setLoading(false);
      setProgress(0);
    }
  };

  const resetForm = () => {
    setAnswerKeyFile(null);
    setStudentFiles([]);
    setResults([]);
    setError('');
    setStep(1);
    setProgress(0);
  };

  const getScoreColor = (score) => {
    if (score >= 0.8) return 'text-success-600';
    if (score >= 0.6) return 'text-warning-600';
    return 'text-danger-600';
  };

  const getScoreBadge = (score) => {
    if (score >= 0.8) return 'status-badge-success';
    if (score >= 0.6) return 'status-badge-warning';
    return 'status-badge-danger';
  };

  const calculateStats = () => {
    const validResults = results.filter(r => !r.error);
    const totalScore = validResults.reduce((sum, r) => sum + (r.score || 0), 0);
    const averageScore = validResults.length > 0 ? totalScore / validResults.length : 0;
    const passCount = validResults.filter(r => r.score >= 0.6).length;
    const failCount = validResults.filter(r => r.score < 0.6).length;
    
    return {
      total: results.length,
      processed: validResults.length,
      errors: results.length - validResults.length,
      averageScore,
      passCount,
      failCount
    };
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="page-header">
        <h1 className="page-title">Batch Grading</h1>
        <p className="mt-2 text-gray-600">
          Process multiple student submissions simultaneously
        </p>
      </div>

      {/* Progress Steps */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-soft border border-gray-200/50 p-6">
        <div className="flex items-center justify-between">
          <div className={`flex items-center ${step >= 1 ? 'text-primary-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 1 ? 'bg-primary-600 text-white' : 'bg-gray-200'}`}>
              1
            </div>
            <span className="ml-2 font-medium">Upload Files</span>
          </div>
          <div className={`flex items-center ${step >= 2 ? 'text-primary-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 2 ? 'bg-primary-600 text-white' : 'bg-gray-200'}`}>
              2
            </div>
            <span className="ml-2 font-medium">Process Batch</span>
          </div>
          <div className={`flex items-center ${step >= 3 ? 'text-primary-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 3 ? 'bg-primary-600 text-white' : 'bg-gray-200'}`}>
              3
            </div>
            <span className="ml-2 font-medium">Results</span>
          </div>
        </div>
        
        {loading && (
          <div className="mt-4">
            <div className="bg-gray-200 rounded-full h-2">
              <div 
                className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-600 mt-2">Processing... {Math.round(progress)}%</p>
          </div>
        )}
      </div>

      {error && (
        <div className="rounded-xl bg-gradient-to-r from-danger-50 to-danger-100 border border-danger-200 p-4">
          <div className="flex">
            <XCircleIcon className="h-5 w-5 text-danger-400" />
            <div className="ml-3">
              <p className="text-sm text-danger-700 font-medium">{error}</p>
            </div>
          </div>
        </div>
      )}

      {step === 1 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Upload Form */}
          <div className="card">
            <h2 className="section-title">Upload Files</h2>
            
            {/* Subject Selection */}
            <div className="mb-6">
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Subject
              </label>
              <select
                value={selectedSubject}
                onChange={(e) => setSelectedSubject(e.target.value)}
                className="input-field"
                required
              >
                <option value="">Select a subject</option>
                {subjects.map((subject) => (
                  <option key={subject.id} value={subject.id}>
                    {subject.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Language Selection */}
            <div className="mb-6">
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Language
              </label>
              <select
                value={language}
                onChange={(e) => setLanguage(e.target.value)}
                className="input-field"
              >
                <option value="en">English</option>
                <option value="ar">Arabic</option>
              </select>
            </div>

            {/* Answer Key Upload */}
            <div className="mb-6">
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Answer Key
              </label>
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-xl hover:border-primary-400 transition-colors duration-200">
                <div className="space-y-1 text-center">
                  <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="flex text-sm text-gray-600">
                    <label className="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500">
                      <span>Upload answer key</span>
                      <input
                        type="file"
                        className="sr-only"
                        accept="image/*,.pdf"
                        onChange={handleAnswerKeyChange}
                      />
                    </label>
                  </div>
                  <p className="text-xs text-gray-500">PNG, JPG, PDF up to 16MB</p>
                  {answerKeyFile && (
                    <p className="text-sm text-success-600 font-medium">
                      ✓ {answerKeyFile.name}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Student Answers Upload */}
            <div className="mb-6">
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Student Answers (Multiple Files)
              </label>
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-xl hover:border-primary-400 transition-colors duration-200">
                <div className="space-y-1 text-center">
                  <FolderIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="flex text-sm text-gray-600">
                    <label className="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500">
                      <span>Upload student answers</span>
                      <input
                        type="file"
                        className="sr-only"
                        accept="image/*,.pdf"
                        multiple
                        onChange={handleStudentFilesChange}
                      />
                    </label>
                  </div>
                  <p className="text-xs text-gray-500">Select multiple files (PNG, JPG, PDF)</p>
                </div>
              </div>
              
              {/* Selected Files List */}
              {studentFiles.length > 0 && (
                <div className="mt-4 space-y-2">
                  <p className="text-sm font-medium text-gray-700">
                    Selected Files ({studentFiles.length}):
                  </p>
                  <div className="max-h-32 overflow-y-auto space-y-1">
                    {studentFiles.map((file, index) => (
                      <div key={index} className="flex items-center justify-between bg-gray-50 px-3 py-2 rounded-lg">
                        <span className="text-sm text-gray-700 truncate">{file.name}</span>
                        <button
                          onClick={() => removeStudentFile(index)}
                          className="text-danger-600 hover:text-danger-800"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <button
              onClick={() => {
                if (answerKeyFile && studentFiles.length > 0 && selectedSubject) {
                  setStep(2);
                  processBatchGrading();
                }
              }}
              disabled={!answerKeyFile || studentFiles.length === 0 || !selectedSubject || loading}
              className="btn-primary w-full"
            >
              {loading ? (
                <>
                  <ClockIcon className="h-5 w-5 mr-2 animate-spin" />
                  Processing Batch...
                </>
              ) : (
                <>
                  <ChartBarIcon className="h-5 w-5 mr-2" />
                  Start Batch Grading ({studentFiles.length} files)
                </>
              )}
            </button>
          </div>

          {/* Instructions */}
          <div className="card">
            <h2 className="section-title">Batch Processing Instructions</h2>
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="text-primary-600 font-bold text-sm">1</span>
                  </div>
                </div>
                <div className="ml-3">
                  <h3 className="font-semibold text-gray-900">Prepare Files</h3>
                  <p className="text-gray-600 text-sm">Ensure all student answer files are properly named and in supported formats (PNG, JPG, PDF).</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="text-primary-600 font-bold text-sm">2</span>
                  </div>
                </div>
                <div className="ml-3">
                  <h3 className="font-semibold text-gray-900">Upload Answer Key</h3>
                  <p className="text-gray-600 text-sm">Upload the master answer key that will be used to compare against all student submissions.</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="text-primary-600 font-bold text-sm">3</span>
                  </div>
                </div>
                <div className="ml-3">
                  <h3 className="font-semibold text-gray-900">Select Multiple Files</h3>
                  <p className="text-gray-600 text-sm">Select all student answer files at once. File names will be used as student names.</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="text-primary-600 font-bold text-sm">4</span>
                  </div>
                </div>
                <div className="ml-3">
                  <h3 className="font-semibold text-gray-900">Automated Processing</h3>
                  <p className="text-gray-600 text-sm">The system will process all files automatically and provide detailed results for each submission.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {step === 2 && loading && (
        <div className="card text-center">
          <div className="animate-spin mx-auto h-12 w-12 text-primary-600 mb-4">
            <ClockIcon />
          </div>
          <h2 className="text-xl font-bold text-gray-900 mb-2">Processing Batch...</h2>
          <p className="text-gray-600">
            Processing {studentFiles.length} student submissions. This may take several minutes.
          </p>
        </div>
      )}

      {step === 3 && results.length > 0 && (
        <div className="space-y-6">
          {/* Results Summary */}
          <div className="card">
            <h2 className="section-title">Batch Results Summary</h2>
            
            {(() => {
              const stats = calculateStats();
              return (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900 mb-2">{stats.total}</div>
                    <div className="text-sm text-gray-600">Total Files</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-3xl font-bold text-success-600 mb-2">{stats.processed}</div>
                    <div className="text-sm text-gray-600">Processed</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary-600 mb-2">
                      {Math.round(stats.averageScore * 100)}%
                    </div>
                    <div className="text-sm text-gray-600">Average Score</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900 mb-2">
                      {stats.passCount}/{stats.total}
                    </div>
                    <div className="text-sm text-gray-600">Pass Rate</div>
                  </div>
                </div>
              );
            })()}
          </div>

          {/* Individual Results */}
          <div className="card">
            <h2 className="section-title">Individual Results</h2>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Student
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Score
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Result
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {results.map((result, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {result.student_name}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {result.error ? (
                          <span className="text-sm text-gray-500">-</span>
                        ) : (
                          <span className={`text-sm font-medium ${getScoreColor(result.score)}`}>
                            {Math.round(result.score * 100)}%
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {result.error ? (
                          <span className="status-badge-danger">Error</span>
                        ) : (
                          <span className={getScoreBadge(result.score)}>
                            {result.matches ? 'PASS' : 'FAIL'}
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {result.error ? result.error : 'Processed'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-center space-x-4">
            <button
              onClick={resetForm}
              className="btn-secondary"
            >
              Process Another Batch
            </button>
            <button
              onClick={() => {
                // TODO: Implement batch report generation
                alert('Batch report generation will be implemented');
              }}
              className="btn-primary"
            >
              Generate Batch Report
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default BatchGrading;
