#!/usr/bin/env python3
"""
Test the exact case you mentioned: "43 is the answer" vs "43"
"""

from PIL import Image, ImageDraw, ImageFont
from ocr.corrector import AnswerCorrector
import os

def create_test_images():
    """Create test images for the exact case mentioned"""
    
    # Create answer key image: "43 is the answer"
    width, height = 400, 100
    answer_key_img = Image.new('RGB', (width, height), color='white')
    draw1 = ImageDraw.Draw(answer_key_img)
    
    try:
        font = ImageFont.truetype("arial.ttf", 32)
    except:
        font = ImageFont.load_default()
    
    answer_key_text = "43 is the answer"
    draw1.text((50, 30), answer_key_text, fill='black', font=font)
    answer_key_path = 'test_answer_key.png'
    answer_key_img.save(answer_key_path)
    
    # Create student answer image: "43"
    student_img = Image.new('RGB', (width, height), color='white')
    draw2 = ImageDraw.Draw(student_img)
    
    student_text = "43"
    draw2.text((50, 30), student_text, fill='black', font=font)
    student_path = 'test_student_answer.png'
    student_img.save(student_path)
    
    print(f"Created test images:")
    print(f"  Answer key: '{answer_key_text}' -> {answer_key_path}")
    print(f"  Student answer: '{student_text}' -> {student_path}")
    
    return answer_key_path, student_path, answer_key_text, student_text

def test_comparison():
    """Test the comparison with the exact case mentioned"""
    
    print("=" * 60)
    print("TESTING: '43 is the answer' vs '43'")
    print("=" * 60)
    
    # Create test images
    answer_key_path, student_path, expected_key, expected_student = create_test_images()
    
    try:
        # Initialize corrector
        corrector = AnswerCorrector(language='eng')
        
        # Test the comparison
        result = corrector.compare_answers(student_path, answer_key_path)
        
        print("\n" + "=" * 60)
        print("RESULTS:")
        print("=" * 60)
        print(f"Expected answer key: '{expected_key}'")
        print(f"OCR extracted key: '{result['key_text']}'")
        print(f"Expected student: '{expected_student}'")
        print(f"OCR extracted student: '{result['student_text']}'")
        print(f"Similarity score: {result['similarity']:.3f}")
        print(f"Match result: {result['matches']}")
        
        if result['matches']:
            print("\n✅ SUCCESS: System correctly identified this as a match!")
        else:
            print("\n❌ ISSUE: System did not identify this as a match")
            print("This should be considered correct since '43' is the core answer")
        
        # Test what the key content extraction would show
        print(f"\n--- Key Content Analysis ---")
        key_content_key = corrector.extract_key_content(result['key_text'])
        key_content_student = corrector.extract_key_content(result['student_text'])
        
        print(f"Key content from answer key: '{key_content_key}'")
        print(f"Key content from student: '{key_content_student}'")
        
        if key_content_key == key_content_student:
            print("✅ Key contents match!")
        else:
            print("❌ Key contents don't match")
        
    except Exception as e:
        print(f"Error during test: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up test images
        for img_path in [answer_key_path, student_path]:
            if os.path.exists(img_path):
                os.remove(img_path)

if __name__ == "__main__":
    test_comparison()
