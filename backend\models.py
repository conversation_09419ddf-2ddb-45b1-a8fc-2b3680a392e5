from extensions import db
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash

# Association tables for many-to-many relationships
user_subject = db.Table('user_subject',
    db.Column('user_id', db.Integer, db.<PERSON><PERSON>('user.id'), primary_key=True),
    db.Column('subject_id', db.Integer, db.<PERSON>ey('subject.id'), primary_key=True)
)

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(256), nullable=False)
    role = db.Column(db.String(20), nullable=False)  # 'teacher' or 'student'
    corrections = db.relationship('Correction', backref='user', lazy=True)
    subjects = db.relationship('Subject', secondary=user_subject, backref=db.backref('users', lazy='dynamic'))

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Admin(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(256), nullable=False)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Subject(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    corrections = db.relationship('Correction', backref='subject', lazy=True)

class Correction(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    subject_id = db.Column(db.Integer, db.ForeignKey('subject.id'), nullable=False)
    date = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    score = db.Column(db.Float, nullable=False)
    notes = db.Column(db.Text)
    answer_key_path = db.Column(db.String(255), nullable=False)
    student_answer_path = db.Column(db.String(255), nullable=False)
    language = db.Column(db.String(10), default='en')  # 'en' or 'ar' 