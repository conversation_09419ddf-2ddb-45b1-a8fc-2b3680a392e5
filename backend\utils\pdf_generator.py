from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from datetime import datetime
import os
from PIL import Image as PILImage

class PDFReportGenerator:
    """Generate PDF reports for grading results"""
    
    def __init__(self, upload_folder):
        self.upload_folder = upload_folder
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
    
    def _setup_custom_styles(self):
        """Setup custom styles for the PDF"""
        # Title style
        self.title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        )
        
        # Header style
        self.header_style = ParagraphStyle(
            'CustomHeader',
            parent=self.styles['Heading2'],
            fontSize=16,
            spaceAfter=12,
            textColor=colors.darkblue
        )
        
        # Normal text style
        self.normal_style = ParagraphStyle(
            'CustomNormal',
            parent=self.styles['Normal'],
            fontSize=12,
            spaceAfter=6
        )
        
        # Score style
        self.score_style = ParagraphStyle(
            'ScoreStyle',
            parent=self.styles['Normal'],
            fontSize=18,
            alignment=TA_CENTER,
            textColor=colors.darkgreen,
            spaceAfter=12
        )
    
    def generate_single_report(self, correction_data, teacher_signature_path=None):
        """Generate PDF report for a single correction"""
        try:
            # Create filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"correction_report_{correction_data.get('correction_id', 'unknown')}_{timestamp}.pdf"
            report_path = os.path.join(self.upload_folder, 'reports', filename)
            
            # Create PDF document
            doc = SimpleDocTemplate(report_path, pagesize=A4)
            story = []
            
            # Title
            title = Paragraph("AHcorrecting - Grading Report", self.title_style)
            story.append(title)
            story.append(Spacer(1, 20))
            
            # Report information table
            report_info = [
                ['Report Date:', datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
                ['Correction ID:', str(correction_data.get('correction_id', 'N/A'))],
                ['Subject:', correction_data.get('subject', 'N/A')],
                ['Student Name:', correction_data.get('student_name', 'Unknown Student')],
                ['Language:', 'Arabic' if correction_data.get('language') == 'ar' else 'English'],
                ['Graded By:', correction_data.get('teacher_name', 'System')]
            ]
            
            info_table = Table(report_info, colWidths=[2*inch, 4*inch])
            info_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                ('BACKGROUND', (1, 0), (1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(info_table)
            story.append(Spacer(1, 30))
            
            # Score section
            score = correction_data.get('score', 0)
            percentage = round(score * 100, 2)
            
            score_header = Paragraph("Grading Results", self.header_style)
            story.append(score_header)
            
            score_text = f"Score: {percentage}% ({score:.3f})"
            score_para = Paragraph(score_text, self.score_style)
            story.append(score_para)
            
            # Grade interpretation
            if percentage >= 90:
                grade = "Excellent (A)"
                grade_color = colors.darkgreen
            elif percentage >= 80:
                grade = "Very Good (B)"
                grade_color = colors.green
            elif percentage >= 70:
                grade = "Good (C)"
                grade_color = colors.orange
            elif percentage >= 60:
                grade = "Satisfactory (D)"
                grade_color = colors.darkorange
            else:
                grade = "Needs Improvement (F)"
                grade_color = colors.red
            
            grade_style = ParagraphStyle(
                'GradeStyle',
                parent=self.normal_style,
                fontSize=16,
                alignment=TA_CENTER,
                textColor=grade_color
            )
            
            grade_para = Paragraph(f"Grade: {grade}", grade_style)
            story.append(grade_para)
            story.append(Spacer(1, 20))
            
            # Text comparison section
            comparison_header = Paragraph("Text Comparison", self.header_style)
            story.append(comparison_header)
            
            # Student answer
            student_text = correction_data.get('student_text', 'No text extracted')
            student_para = Paragraph(f"<b>Student Answer:</b><br/>{student_text}", self.normal_style)
            story.append(student_para)
            story.append(Spacer(1, 10))
            
            # Answer key
            key_text = correction_data.get('key_text', 'No text extracted')
            key_para = Paragraph(f"<b>Answer Key:</b><br/>{key_text}", self.normal_style)
            story.append(key_para)
            story.append(Spacer(1, 10))
            
            # Similarity details
            similarity = correction_data.get('similarity', 0)
            matches = correction_data.get('matches', False)
            
            similarity_text = f"Text Similarity: {round(similarity * 100, 2)}%"
            match_text = "✓ Answer matches key" if matches else "✗ Answer does not match key"
            
            similarity_para = Paragraph(similarity_text, self.normal_style)
            match_para = Paragraph(match_text, self.normal_style)
            
            story.append(similarity_para)
            story.append(match_para)
            story.append(Spacer(1, 20))
            
            # Notes section
            notes = correction_data.get('notes', '')
            if notes:
                notes_header = Paragraph("Additional Notes", self.header_style)
                story.append(notes_header)
                notes_para = Paragraph(notes, self.normal_style)
                story.append(notes_para)
                story.append(Spacer(1, 20))
            
            # Teacher signature
            if teacher_signature_path and os.path.exists(teacher_signature_path):
                try:
                    signature_header = Paragraph("Teacher Signature", self.header_style)
                    story.append(signature_header)
                    
                    # Resize signature image if needed
                    with PILImage.open(teacher_signature_path) as img:
                        width, height = img.size
                        max_width = 200
                        if width > max_width:
                            ratio = max_width / width
                            new_height = int(height * ratio)
                            signature_img = Image(teacher_signature_path, width=max_width, height=new_height)
                        else:
                            signature_img = Image(teacher_signature_path)
                    
                    story.append(signature_img)
                except Exception as e:
                    print(f"Error adding signature: {str(e)}")
            
            # Footer
            story.append(Spacer(1, 30))
            footer_text = "Generated by AHcorrecting - AI-Powered Handwriting Correction Tool"
            footer_para = Paragraph(footer_text, ParagraphStyle(
                'Footer',
                parent=self.normal_style,
                fontSize=8,
                alignment=TA_CENTER,
                textColor=colors.grey
            ))
            story.append(footer_para)
            
            # Build PDF
            doc.build(story)
            
            # Return relative path
            return os.path.relpath(report_path, self.upload_folder)
            
        except Exception as e:
            print(f"Error generating PDF report: {str(e)}")
            return None
    
    def generate_batch_report(self, corrections_data, teacher_signature_path=None):
        """Generate PDF report for multiple corrections"""
        try:
            # Create filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"batch_correction_report_{timestamp}.pdf"
            report_path = os.path.join(self.upload_folder, 'reports', filename)
            
            # Create PDF document
            doc = SimpleDocTemplate(report_path, pagesize=A4)
            story = []
            
            # Title
            title = Paragraph("AHcorrecting - Batch Grading Report", self.title_style)
            story.append(title)
            story.append(Spacer(1, 20))
            
            # Summary information
            total_corrections = len(corrections_data)
            avg_score = sum(c.get('score', 0) for c in corrections_data) / total_corrections if total_corrections > 0 else 0
            
            summary_info = [
                ['Report Date:', datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
                ['Total Corrections:', str(total_corrections)],
                ['Average Score:', f"{round(avg_score * 100, 2)}%"],
                ['Subject:', corrections_data[0].get('subject', 'N/A') if corrections_data else 'N/A']
            ]
            
            summary_table = Table(summary_info, colWidths=[2*inch, 4*inch])
            summary_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                ('BACKGROUND', (1, 0), (1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(summary_table)
            story.append(Spacer(1, 30))
            
            # Results table
            results_header = Paragraph("Individual Results", self.header_style)
            story.append(results_header)
            
            # Create results table
            table_data = [['Student Name', 'Score (%)', 'Grade', 'Similarity (%)', 'Match']]
            
            for correction in corrections_data:
                score = correction.get('score', 0)
                percentage = round(score * 100, 2)
                similarity = round(correction.get('similarity', 0) * 100, 2)
                matches = "Yes" if correction.get('matches', False) else "No"
                
                # Determine grade
                if percentage >= 90:
                    grade = "A"
                elif percentage >= 80:
                    grade = "B"
                elif percentage >= 70:
                    grade = "C"
                elif percentage >= 60:
                    grade = "D"
                else:
                    grade = "F"
                
                table_data.append([
                    correction.get('student_name', 'Unknown'),
                    f"{percentage}%",
                    grade,
                    f"{similarity}%",
                    matches
                ])
            
            results_table = Table(table_data, colWidths=[2*inch, 1*inch, 0.7*inch, 1*inch, 0.7*inch])
            results_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(results_table)
            story.append(Spacer(1, 30))
            
            # Teacher signature
            if teacher_signature_path and os.path.exists(teacher_signature_path):
                try:
                    signature_header = Paragraph("Teacher Signature", self.header_style)
                    story.append(signature_header)
                    
                    signature_img = Image(teacher_signature_path, width=200, height=100)
                    story.append(signature_img)
                except Exception as e:
                    print(f"Error adding signature: {str(e)}")
            
            # Footer
            story.append(Spacer(1, 30))
            footer_text = "Generated by AHcorrecting - AI-Powered Handwriting Correction Tool"
            footer_para = Paragraph(footer_text, ParagraphStyle(
                'Footer',
                parent=self.normal_style,
                fontSize=8,
                alignment=TA_CENTER,
                textColor=colors.grey
            ))
            story.append(footer_para)
            
            # Build PDF
            doc.build(story)
            
            # Return relative path
            return os.path.relpath(report_path, self.upload_folder)
            
        except Exception as e:
            print(f"Error generating batch PDF report: {str(e)}")
            return None
