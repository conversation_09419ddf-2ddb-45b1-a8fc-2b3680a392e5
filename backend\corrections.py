from flask import Blueprint, request, jsonify, current_app, send_file
from flask_jwt_extended import jwt_required, get_jwt_identity
from werkzeug.utils import secure_filename
from models import User, Admin, Subject, Correction, Grade, db
from ocr.corrector import AnswerCorrector
from utils.pdf_generator import PDFReportGenerator
from utils.file_handler import FileHandler
import os
import uuid
from datetime import datetime
from flask_cors import cross_origin

corrections = Blueprint('corrections', __name__)

# Helper function to check if user is admin or teacher
def is_authorized_user():
    user_id = get_jwt_identity()
    admin = Admin.query.get(user_id)
    if admin:
        return True, admin, 'admin'
    
    user = User.query.get(user_id)
    if user and user.role == 'teacher':
        return True, user, 'teacher'
    
    return False, None, None

@corrections.route('/upload/answer-key', methods=['POST'])
@jwt_required()
@cross_origin()
def upload_answer_key():
    """Upload answer key image/PDF"""
    try:
        authorized, user, role = is_authorized_user()
        if not authorized:
            return jsonify({'error': 'Unauthorized. Only teachers and admins can upload answer keys.'}), 403
        
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        # Get additional data
        subject_id = request.form.get('subject_id')
        language = request.form.get('language', 'en')
        
        if not subject_id:
            return jsonify({'error': 'Subject ID is required'}), 400
        
        # Validate subject exists
        subject = Subject.query.get(subject_id)
        if not subject:
            return jsonify({'error': 'Subject not found'}), 404
        
        # Handle file upload
        file_handler = FileHandler(current_app.config['UPLOAD_FOLDER'])
        file_path = file_handler.save_file(file, 'answer_keys', f"answer_key_{uuid.uuid4().hex}")
        
        if not file_path:
            return jsonify({'error': 'Invalid file type or upload failed'}), 400
        
        return jsonify({
            'message': 'Answer key uploaded successfully',
            'file_path': file_path,
            'subject_id': subject_id,
            'language': language
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Upload failed: {str(e)}'}), 500

@corrections.route('/upload/student-answer', methods=['POST'])
@jwt_required()
@cross_origin()
def upload_student_answer():
    """Upload student answer image/PDF"""
    try:
        authorized, user, role = is_authorized_user()
        if not authorized:
            return jsonify({'error': 'Unauthorized. Only teachers and admins can upload student answers.'}), 403
        
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        # Get additional data
        student_name = request.form.get('student_name', 'Unknown Student')
        subject_id = request.form.get('subject_id')
        language = request.form.get('language', 'en')
        
        if not subject_id:
            return jsonify({'error': 'Subject ID is required'}), 400
        
        # Validate subject exists
        subject = Subject.query.get(subject_id)
        if not subject:
            return jsonify({'error': 'Subject not found'}), 404
        
        # Handle file upload
        file_handler = FileHandler(current_app.config['UPLOAD_FOLDER'])
        file_path = file_handler.save_file(file, 'student_answers', f"student_{uuid.uuid4().hex}")
        
        if not file_path:
            return jsonify({'error': 'Invalid file type or upload failed'}), 400
        
        return jsonify({
            'message': 'Student answer uploaded successfully',
            'file_path': file_path,
            'student_name': student_name,
            'subject_id': subject_id,
            'language': language
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Upload failed: {str(e)}'}), 500

@corrections.route('/process', methods=['POST'])
@jwt_required()
@cross_origin()
def process_correction():
    """Process correction by comparing student answer with answer key"""
    try:
        authorized, user, role = is_authorized_user()
        if not authorized:
            return jsonify({'error': 'Unauthorized. Only teachers and admins can process corrections.'}), 403
        
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        required_fields = ['student_answer_path', 'answer_key_path', 'subject_id']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400
        
        student_answer_path = data['student_answer_path']
        answer_key_path = data['answer_key_path']
        subject_id = data['subject_id']
        language = data.get('language', 'en')
        student_name = data.get('student_name', 'Unknown Student')
        notes = data.get('notes', '')
        
        # Validate subject exists
        subject = Subject.query.get(subject_id)
        if not subject:
            return jsonify({'error': 'Subject not found'}), 404
        
        # Validate files exist
        full_student_path = os.path.join(current_app.config['UPLOAD_FOLDER'], student_answer_path)
        full_answer_key_path = os.path.join(current_app.config['UPLOAD_FOLDER'], answer_key_path)
        
        if not os.path.exists(full_student_path):
            return jsonify({'error': 'Student answer file not found'}), 404
        
        if not os.path.exists(full_answer_key_path):
            return jsonify({'error': 'Answer key file not found'}), 404
        
        # Set up OCR language
        ocr_language = 'ara' if language == 'ar' else 'eng'
        corrector = AnswerCorrector(language=ocr_language)
        
        # Process the correction
        result = corrector.process_correction(full_student_path, full_answer_key_path)
        
        if 'error' in result:
            return jsonify({'error': f'OCR processing failed: {result["error"]}'}), 500
        
        # Save correction to database
        correction = Correction(
            user_id=user.id,
            subject_id=subject_id,
            score=result['score'],
            notes=notes,
            answer_key_path=answer_key_path,
            student_answer_path=student_answer_path,
            language=language
        )
        
        db.session.add(correction)
        db.session.commit()
        
        return jsonify({
            'correction_id': correction.id,
            'score': result['score'],
            'similarity': result['similarity'],
            'matches': result['matches'],
            'student_text': result['student_text'],
            'key_text': result['key_text'],
            'student_name': student_name,
            'subject': subject.name,
            'date': correction.date.isoformat(),
            'language': language
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'Processing failed: {str(e)}'}), 500

@corrections.route('/batch-process', methods=['POST'])
@jwt_required()
@cross_origin()
def batch_process_corrections():
    """Process multiple corrections in batch"""
    try:
        authorized, user, role = is_authorized_user()
        if not authorized:
            return jsonify({'error': 'Unauthorized. Only teachers and admins can process corrections.'}), 403
        
        data = request.get_json()
        if not data or 'corrections' not in data:
            return jsonify({'error': 'No corrections data provided'}), 400
        
        corrections_data = data['corrections']
        if not isinstance(corrections_data, list):
            return jsonify({'error': 'Corrections must be a list'}), 400
        
        results = []
        failed_corrections = []
        
        for idx, correction_data in enumerate(corrections_data):
            try:
                # Process each correction
                required_fields = ['student_answer_path', 'answer_key_path', 'subject_id']
                for field in required_fields:
                    if not correction_data.get(field):
                        failed_corrections.append({
                            'index': idx,
                            'error': f'{field} is required'
                        })
                        continue
                
                student_answer_path = correction_data['student_answer_path']
                answer_key_path = correction_data['answer_key_path']
                subject_id = correction_data['subject_id']
                language = correction_data.get('language', 'en')
                student_name = correction_data.get('student_name', f'Student {idx + 1}')
                notes = correction_data.get('notes', '')
                
                # Validate subject exists
                subject = Subject.query.get(subject_id)
                if not subject:
                    failed_corrections.append({
                        'index': idx,
                        'error': 'Subject not found'
                    })
                    continue
                
                # Validate files exist
                full_student_path = os.path.join(current_app.config['UPLOAD_FOLDER'], student_answer_path)
                full_answer_key_path = os.path.join(current_app.config['UPLOAD_FOLDER'], answer_key_path)
                
                if not os.path.exists(full_student_path) or not os.path.exists(full_answer_key_path):
                    failed_corrections.append({
                        'index': idx,
                        'error': 'One or more files not found'
                    })
                    continue
                
                # Set up OCR language
                ocr_language = 'ara' if language == 'ar' else 'eng'
                corrector = AnswerCorrector(language=ocr_language)
                
                # Process the correction
                result = corrector.process_correction(full_student_path, full_answer_key_path)
                
                if 'error' in result:
                    failed_corrections.append({
                        'index': idx,
                        'error': f'OCR processing failed: {result["error"]}'
                    })
                    continue
                
                # Save correction to database
                correction = Correction(
                    user_id=user.id,
                    subject_id=subject_id,
                    score=result['score'],
                    notes=notes,
                    answer_key_path=answer_key_path,
                    student_answer_path=student_answer_path,
                    language=language
                )
                
                db.session.add(correction)
                db.session.commit()
                
                results.append({
                    'index': idx,
                    'correction_id': correction.id,
                    'score': result['score'],
                    'similarity': result['similarity'],
                    'matches': result['matches'],
                    'student_name': student_name,
                    'subject': subject.name
                })
                
            except Exception as e:
                failed_corrections.append({
                    'index': idx,
                    'error': str(e)
                })
        
        return jsonify({
            'successful_corrections': len(results),
            'failed_corrections': len(failed_corrections),
            'results': results,
            'failures': failed_corrections
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'Batch processing failed: {str(e)}'}), 500

@corrections.route('/generate-report/<int:correction_id>', methods=['POST'])
@jwt_required()
@cross_origin()
def generate_single_report(correction_id):
    """Generate PDF report for a single correction"""
    try:
        authorized, user, role = is_authorized_user()
        if not authorized:
            return jsonify({'error': 'Unauthorized'}), 403

        # Get correction data
        correction = Correction.query.get(correction_id)
        if not correction:
            return jsonify({'error': 'Correction not found'}), 404

        # Check if user owns this correction (unless admin)
        if role != 'admin' and correction.user_id != user.id:
            return jsonify({'error': 'Access denied'}), 403

        # Get teacher signature path if provided
        data = request.get_json() or {}
        signature_path = data.get('signature_path')

        if signature_path:
            full_signature_path = os.path.join(current_app.config['UPLOAD_FOLDER'], signature_path)
            if not os.path.exists(full_signature_path):
                signature_path = None

        # Prepare correction data for PDF
        correction_data = {
            'correction_id': correction.id,
            'score': correction.score,
            'similarity': correction.score,  # Using score as similarity for now
            'matches': correction.score >= 0.8,
            'student_text': 'Text extracted from student answer',  # Would need to re-extract or store
            'key_text': 'Text extracted from answer key',  # Would need to re-extract or store
            'student_name': data.get('student_name', 'Unknown Student'),
            'subject': correction.subject.name,
            'language': correction.language,
            'notes': correction.notes,
            'teacher_name': user.name,
            'date': correction.date
        }

        # Generate PDF
        pdf_generator = PDFReportGenerator(current_app.config['UPLOAD_FOLDER'])
        report_path = pdf_generator.generate_single_report(
            correction_data,
            full_signature_path if signature_path else None
        )

        if not report_path:
            return jsonify({'error': 'Failed to generate PDF report'}), 500

        return jsonify({
            'message': 'Report generated successfully',
            'report_path': report_path,
            'download_url': f'/api/corrections/download-report/{os.path.basename(report_path)}'
        }), 200

    except Exception as e:
        return jsonify({'error': f'Report generation failed: {str(e)}'}), 500

@corrections.route('/generate-batch-report', methods=['POST'])
@jwt_required()
@cross_origin()
def generate_batch_report():
    """Generate PDF report for multiple corrections"""
    try:
        authorized, user, role = is_authorized_user()
        if not authorized:
            return jsonify({'error': 'Unauthorized'}), 403

        data = request.get_json()
        if not data or 'correction_ids' not in data:
            return jsonify({'error': 'Correction IDs are required'}), 400

        correction_ids = data['correction_ids']
        signature_path = data.get('signature_path')

        # Get corrections
        corrections_query = Correction.query.filter(Correction.id.in_(correction_ids))

        # Filter by user if not admin
        if role != 'admin':
            corrections_query = corrections_query.filter(Correction.user_id == user.id)

        corrections_list = corrections_query.all()

        if not corrections_list:
            return jsonify({'error': 'No corrections found'}), 404

        # Prepare corrections data for PDF
        corrections_data = []
        for correction in corrections_list:
            corrections_data.append({
                'correction_id': correction.id,
                'score': correction.score,
                'similarity': correction.score,
                'matches': correction.score >= 0.8,
                'student_name': f'Student {correction.id}',  # Would need actual student names
                'subject': correction.subject.name,
                'language': correction.language
            })

        # Check signature path
        full_signature_path = None
        if signature_path:
            full_signature_path = os.path.join(current_app.config['UPLOAD_FOLDER'], signature_path)
            if not os.path.exists(full_signature_path):
                full_signature_path = None

        # Generate PDF
        pdf_generator = PDFReportGenerator(current_app.config['UPLOAD_FOLDER'])
        report_path = pdf_generator.generate_batch_report(corrections_data, full_signature_path)

        if not report_path:
            return jsonify({'error': 'Failed to generate batch PDF report'}), 500

        return jsonify({
            'message': 'Batch report generated successfully',
            'report_path': report_path,
            'download_url': f'/api/corrections/download-report/{os.path.basename(report_path)}',
            'corrections_count': len(corrections_data)
        }), 200

    except Exception as e:
        return jsonify({'error': f'Batch report generation failed: {str(e)}'}), 500

@corrections.route('/download-report/<filename>', methods=['GET'])
@jwt_required()
@cross_origin()
def download_report(filename):
    """Download generated PDF report"""
    try:
        authorized, user, role = is_authorized_user()
        if not authorized:
            return jsonify({'error': 'Unauthorized'}), 403

        report_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'reports', filename)

        if not os.path.exists(report_path):
            return jsonify({'error': 'Report not found'}), 404

        return send_file(
            report_path,
            as_attachment=True,
            download_name=filename,
            mimetype='application/pdf'
        )

    except Exception as e:
        return jsonify({'error': f'Download failed: {str(e)}'}), 500

@corrections.route('/upload/signature', methods=['POST'])
@jwt_required()
@cross_origin()
def upload_signature():
    """Upload teacher signature for reports"""
    try:
        authorized, user, role = is_authorized_user()
        if not authorized:
            return jsonify({'error': 'Unauthorized'}), 403

        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Handle file upload
        file_handler = FileHandler(current_app.config['UPLOAD_FOLDER'])
        file_path = file_handler.save_file(file, 'signatures', f"signature_{user.id}_{uuid.uuid4().hex}")

        if not file_path:
            return jsonify({'error': 'Invalid file type or upload failed'}), 400

        return jsonify({
            'message': 'Signature uploaded successfully',
            'signature_path': file_path
        }), 200

    except Exception as e:
        return jsonify({'error': f'Signature upload failed: {str(e)}'}), 500

@corrections.route('/save-grade', methods=['POST'])
@jwt_required()
@cross_origin()
def save_grade():
    """Save a grade for a student"""
    try:
        authorized, user, role = is_authorized_user()
        if not authorized:
            return jsonify({'error': 'Unauthorized'}), 403

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        required_fields = ['student_id', 'subject_id', 'score', 'matches']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'{field} is required'}), 400

        student_id = data['student_id']
        subject_id = data['subject_id']
        score = float(data['score'])
        matches = bool(data['matches'])
        student_text = data.get('student_text', '')
        key_text = data.get('key_text', '')
        notes = data.get('notes', '')
        correction_id = data.get('correction_id')

        # Validate student exists
        student = User.query.filter_by(id=student_id, role='student').first()
        if not student:
            return jsonify({'error': 'Student not found'}), 404

        # Validate subject exists
        subject = Subject.query.get(subject_id)
        if not subject:
            return jsonify({'error': 'Subject not found'}), 404

        # Create grade record
        grade = Grade(
            student_id=student_id,
            subject_id=subject_id,
            teacher_id=user.id,
            correction_id=correction_id,
            score=score,
            percentage=int(score * 100),
            matches=matches,
            student_text=student_text,
            key_text=key_text,
            notes=notes
        )

        db.session.add(grade)
        db.session.commit()

        return jsonify({
            'message': 'Grade saved successfully',
            'grade_id': grade.id,
            'student_name': student.name,
            'subject_name': subject.name,
            'score': score,
            'percentage': grade.percentage,
            'matches': matches
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'Failed to save grade: {str(e)}'}), 500

@corrections.route('/student-grades/<int:student_id>', methods=['GET'])
@jwt_required()
@cross_origin()
def get_student_grades(student_id):
    """Get all grades for a specific student"""
    try:
        user_id = get_jwt_identity()

        # Check if user is admin, teacher, or the student themselves
        admin = Admin.query.get(user_id)
        user = User.query.get(user_id)

        if not admin and not user:
            return jsonify({'error': 'Unauthorized'}), 403

        # Students can only see their own grades
        if user and user.role == 'student' and user.id != student_id:
            return jsonify({'error': 'Access denied'}), 403

        # Validate student exists
        student = User.query.filter_by(id=student_id, role='student').first()
        if not student:
            return jsonify({'error': 'Student not found'}), 404

        # Get all grades for the student
        grades = Grade.query.filter_by(student_id=student_id).order_by(Grade.created_at.desc()).all()

        grades_data = []
        for grade in grades:
            grades_data.append({
                'id': grade.id,
                'subject': grade.subject.name,
                'subject_id': grade.subject_id,
                'teacher_name': grade.teacher.name,
                'score': grade.score,
                'percentage': grade.percentage,
                'matches': grade.matches,
                'student_text': grade.student_text,
                'key_text': grade.key_text,
                'notes': grade.notes,
                'date': grade.created_at.isoformat(),
                'similarity': grade.score  # For compatibility with frontend
            })

        return jsonify({
            'student_name': student.name,
            'grades': grades_data,
            'total_grades': len(grades_data)
        }), 200

    except Exception as e:
        return jsonify({'error': f'Failed to get student grades: {str(e)}'}), 500
