import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext.jsx';
import { correctionsAPI, adminAPI } from '../services/api.js';
import {
  AcademicCapIcon,
  UserGroupIcon,
  PlusIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';

const EnrollmentManagement = () => {
  const { user } = useAuth();
  const [students, setStudents] = useState([]);
  const [subjects, setSubjects] = useState([]);
  const [selectedStudent, setSelectedStudent] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('');
  const [enrollments, setEnrollments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [studentsData, subjectsData] = await Promise.all([
        adminAPI.getStudents(),
        adminAPI.getSubjects()
      ]);
      setStudents(studentsData);
      setSubjects(subjectsData);
    } catch (error) {
      console.error('Error fetching data:', error);
      setError('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const fetchStudentEnrollments = async (studentId) => {
    if (!studentId) {
      setEnrollments([]);
      return;
    }

    try {
      const response = await correctionsAPI.getStudentEnrollments(studentId);
      setEnrollments(response.enrollments);
    } catch (error) {
      console.error('Error fetching enrollments:', error);
      setEnrollments([]);
    }
  };

  const handleStudentChange = (studentId) => {
    setSelectedStudent(studentId);
    fetchStudentEnrollments(studentId);
  };

  const handleEnrollStudent = async () => {
    if (!selectedStudent || !selectedSubject) {
      setError('Please select both student and subject');
      return;
    }

    try {
      setLoading(true);
      setError('');
      setMessage('');

      await correctionsAPI.enrollStudent(selectedStudent, selectedSubject);
      
      setMessage('Student enrolled successfully!');
      setSelectedSubject('');
      
      // Refresh enrollments
      fetchStudentEnrollments(selectedStudent);
      
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to enroll student');
    } finally {
      setLoading(false);
    }
  };

  const selectedStudentData = students.find(s => s.id == selectedStudent);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Enrollment Management</h1>
          <p className="text-gray-600">Manage student enrollments in subjects</p>
        </div>
      </div>

      {/* Messages */}
      {message && (
        <div className="bg-green-50 border border-green-200 rounded-xl p-4">
          <div className="flex items-center">
            <CheckCircleIcon className="h-5 w-5 text-green-400 mr-2" />
            <span className="text-green-700">{message}</span>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-xl p-4">
          <div className="flex items-center">
            <XCircleIcon className="h-5 w-5 text-red-400 mr-2" />
            <span className="text-red-700">{error}</span>
          </div>
        </div>
      )}

      {/* Enrollment Form */}
      <div className="card">
        <h2 className="section-title">Enroll Student in Subject</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Student Selection */}
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Select Student
            </label>
            <select
              value={selectedStudent}
              onChange={(e) => handleStudentChange(e.target.value)}
              className="input-field"
            >
              <option value="">Choose a student...</option>
              {students.map((student) => (
                <option key={student.id} value={student.id}>
                  {student.name} ({student.email})
                </option>
              ))}
            </select>
          </div>

          {/* Subject Selection */}
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Select Subject
            </label>
            <select
              value={selectedSubject}
              onChange={(e) => setSelectedSubject(e.target.value)}
              className="input-field"
              disabled={!selectedStudent}
            >
              <option value="">Choose a subject...</option>
              {subjects.map((subject) => (
                <option key={subject.id} value={subject.id}>
                  {subject.name} ({subject.code})
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="mt-6">
          <button
            onClick={handleEnrollStudent}
            disabled={!selectedStudent || !selectedSubject || loading}
            className={`btn-primary flex items-center gap-2 ${
              (!selectedStudent || !selectedSubject || loading) ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <PlusIcon className="h-4 w-4" />
            {loading ? 'Enrolling...' : 'Enroll Student'}
          </button>
        </div>
      </div>

      {/* Current Enrollments */}
      {selectedStudent && (
        <div className="card">
          <h2 className="section-title">
            Current Enrollments
            {selectedStudentData && (
              <span className="text-base font-normal text-gray-600 ml-2">
                for {selectedStudentData.name}
              </span>
            )}
          </h2>

          {enrollments.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {enrollments.map((enrollment) => (
                <div key={enrollment.id} className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center">
                      <AcademicCapIcon className="h-6 w-6 text-blue-600 mr-2" />
                      <div>
                        <h3 className="font-semibold text-gray-900">{enrollment.subject_name}</h3>
                        <p className="text-sm text-gray-600">{enrollment.subject_code}</p>
                      </div>
                    </div>
                    <span className="status-badge-success text-xs">
                      Active
                    </span>
                  </div>
                  <div className="border-t border-blue-200 pt-3">
                    <div className="flex items-center text-sm text-gray-600">
                      <UserGroupIcon className="h-4 w-4 mr-1" />
                      <span>Teacher: {enrollment.teacher_name}</span>
                    </div>
                    <div className="text-sm text-gray-500 mt-1">
                      Enrolled: {new Date(enrollment.enrolled_at).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <AcademicCapIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Enrollments</h3>
              <p className="text-gray-600">
                This student is not enrolled in any subjects yet.
              </p>
            </div>
          )}
        </div>
      )}

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card text-center">
          <UserGroupIcon className="mx-auto h-8 w-8 text-blue-600 mb-2" />
          <div className="text-2xl font-bold text-gray-900">{students.length}</div>
          <div className="text-sm text-gray-600">Total Students</div>
        </div>
        
        <div className="card text-center">
          <AcademicCapIcon className="mx-auto h-8 w-8 text-green-600 mb-2" />
          <div className="text-2xl font-bold text-gray-900">{subjects.length}</div>
          <div className="text-sm text-gray-600">Available Subjects</div>
        </div>
        
        <div className="card text-center">
          <CheckCircleIcon className="mx-auto h-8 w-8 text-purple-600 mb-2" />
          <div className="text-2xl font-bold text-gray-900">{enrollments.length}</div>
          <div className="text-sm text-gray-600">
            {selectedStudentData ? `${selectedStudentData.name}'s Enrollments` : 'Select Student'}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnrollmentManagement;
