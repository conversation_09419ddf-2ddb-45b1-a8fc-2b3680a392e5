#!/usr/bin/env python3
"""
Test script to verify Tesseract OCR installation and functionality
"""

import os
import sys
import platform
from PIL import Image, ImageDraw, ImageFont

def test_tesseract_installation():
    """Test if Tesseract is properly installed"""
    print("=== Tesseract Installation Test ===\n")
    
    try:
        import pytesseract
        print("✓ pytesseract module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import pytesseract: {e}")
        print("Install with: pip install pytesseract")
        return False
    
    # Configure Tesseract path for Windows
    if platform.system() == 'Windows':
        print("Detected Windows system, checking common Tesseract paths...")
        
        possible_paths = [
            r'C:\Program Files\Tesseract-OCR\tesseract.exe',
            r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
            r'C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe'.format(os.getenv('USERNAME', '')),
        ]
        
        tesseract_found = False
        for path in possible_paths:
            if os.path.exists(path):
                pytesseract.pytesseract.tesseract_cmd = path
                print(f"✓ Tesseract found at: {path}")
                tesseract_found = True
                break
        
        if not tesseract_found:
            print("✗ Tesseract not found in common locations")
            print("Please install Tesseract from: https://github.com/UB-Mannheim/tesseract/wiki")
            return False
    
    # Test Tesseract version
    try:
        version = pytesseract.get_tesseract_version()
        print(f"✓ Tesseract version: {version}")
    except Exception as e:
        print(f"✗ Failed to get Tesseract version: {e}")
        return False
    
    return True

def create_test_image():
    """Create a simple test image with text"""
    print("\n=== Creating Test Image ===")
    
    # Create a simple image with text
    width, height = 400, 100
    image = Image.new('RGB', (width, height), color='white')
    draw = ImageDraw.Draw(image)
    
    # Try to use a font, fall back to default if not available
    try:
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        font = ImageFont.load_default()
    
    text = "Hello World 123"
    draw.text((50, 30), text, fill='black', font=font)
    
    # Save the image
    test_image_path = 'test_image.png'
    image.save(test_image_path)
    print(f"✓ Test image created: {test_image_path}")
    
    return test_image_path, text

def test_ocr_functionality():
    """Test OCR functionality with a simple image"""
    print("\n=== OCR Functionality Test ===")
    
    try:
        import pytesseract
        
        # Create test image
        image_path, expected_text = create_test_image()
        
        # Perform OCR
        print("Performing OCR on test image...")
        extracted_text = pytesseract.image_to_string(Image.open(image_path))
        extracted_text = extracted_text.strip()
        
        print(f"Expected text: '{expected_text}'")
        print(f"Extracted text: '{extracted_text}'")
        
        # Check if OCR worked
        if expected_text.lower() in extracted_text.lower():
            print("✓ OCR working correctly!")
            return True
        else:
            print("⚠ OCR extracted text but doesn't match expected")
            print("This might be normal - OCR isn't always perfect")
            return True
            
    except Exception as e:
        print(f"✗ OCR test failed: {e}")
        return False
    finally:
        # Clean up test image
        if os.path.exists('test_image.png'):
            os.remove('test_image.png')

def test_answer_corrector():
    """Test the AnswerCorrector class"""
    print("\n=== Answer Corrector Test ===")
    
    try:
        from ocr.corrector import AnswerCorrector
        
        # Initialize corrector
        print("Initializing AnswerCorrector...")
        corrector = AnswerCorrector(language='eng')
        
        # Create two identical test images
        image1_path, text1 = create_test_image()
        image2_path = 'test_image2.png'
        
        # Copy the same image
        import shutil
        shutil.copy(image1_path, image2_path)
        
        print("Testing answer comparison...")
        result = corrector.compare_answers(image1_path, image2_path)
        
        print(f"Comparison result: {result}")
        
        if result['similarity'] > 0.5:
            print("✓ Answer corrector working!")
            return True
        else:
            print("⚠ Answer corrector returned low similarity")
            return False
            
    except Exception as e:
        print(f"✗ Answer corrector test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up test images
        for img in ['test_image.png', 'test_image2.png']:
            if os.path.exists(img):
                os.remove(img)

def main():
    """Run all tests"""
    print("Tesseract OCR Installation and Functionality Test")
    print("=" * 50)
    
    tests = [
        ("Tesseract Installation", test_tesseract_installation),
        ("OCR Functionality", test_ocr_functionality),
        ("Answer Corrector", test_answer_corrector),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✓ PASS" if passed else "✗ FAIL"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! OCR should work correctly.")
    else:
        print("\n❌ Some tests failed. Please install Tesseract OCR:")
        print("1. Download from: https://github.com/UB-Mannheim/tesseract/wiki")
        print("2. Install to default location (C:\\Program Files\\Tesseract-OCR\\)")
        print("3. Make sure to include English language data during installation")
        print("4. Restart your application after installation")

if __name__ == "__main__":
    main()
