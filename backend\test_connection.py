#!/usr/bin/env python3
"""
Simple test to verify backend is accessible
"""

import requests
import json

def test_backend_connection():
    """Test if backend is accessible"""
    base_url = "http://localhost:5000"
    
    print("Testing backend connection...")
    print(f"Base URL: {base_url}")
    
    # Test 1: Basic health check
    try:
        response = requests.get(f"{base_url}/api/admin/debug/db", timeout=5)
        print(f"✓ Backend is accessible: {response.status_code}")
        print(f"Response: {response.text[:100]}...")
    except requests.exceptions.ConnectionError:
        print("✗ Cannot connect to backend - is it running?")
        return False
    except requests.exceptions.Timeout:
        print("✗ Backend connection timeout")
        return False
    except Exception as e:
        print(f"✗ Error connecting to backend: {e}")
        return False
    
    # Test 2: CORS headers
    try:
        response = requests.options(f"{base_url}/api/admin/subjects")
        print(f"✓ CORS preflight: {response.status_code}")
        print(f"CORS headers: {dict(response.headers)}")
    except Exception as e:
        print(f"⚠ CORS test failed: {e}")
    
    # Test 3: Subjects endpoint
    try:
        response = requests.get(f"{base_url}/api/admin/subjects")
        print(f"✓ Subjects endpoint: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Subjects data: {data}")
    except Exception as e:
        print(f"⚠ Subjects test failed: {e}")
    
    return True

if __name__ == "__main__":
    test_backend_connection()
