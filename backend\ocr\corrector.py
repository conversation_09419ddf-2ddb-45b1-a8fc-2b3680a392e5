import pytesseract
from PIL import Image
import cv2
import numpy as np
import os
from difflib import SequenceMatcher
import re
from fuzzywuzzy import fuzz
import unicodedata

class AnswerCorrector:
    def __init__(self, language='eng'):
        self.language = language
        self.is_arabic = language == 'ara'

        # Configure Tesseract path for Windows
        import platform
        if platform.system() == 'Windows':
            # Try common Tesseract installation paths
            possible_paths = [
                r'C:\Program Files\Tesseract-OCR\tesseract.exe',
                r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
                r'C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe'.format(os.getenv('USERNAME', '')),
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    pytesseract.pytesseract.tesseract_cmd = path
                    print(f"Tesseract found at: {path}")
                    break
            else:
                print("Warning: Tesseract not found in common locations. Please install Tesseract OCR.")
                print("Download from: https://github.com/UB-Mannheim/tesseract/wiki")

        # Test Tesseract installation
        try:
            version = pytesseract.get_tesseract_version()
            print(f"Tesseract version: {version}")
        except Exception as e:
            print(f"Tesseract test failed: {e}")
            print("Please ensure Tesseract is properly installed and in PATH.")
    
    def preprocess_image(self, image_path):
        """Preprocess image for better OCR results"""
        # Read image
        img = cv2.imread(image_path)
        if img is None:
            raise ValueError(f"Could not read image at {image_path}")

        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Resize image if too small (improve OCR accuracy)
        height, width = gray.shape
        if height < 300 or width < 300:
            scale_factor = max(300/height, 300/width)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

        # Apply multiple preprocessing techniques and choose the best
        processed_images = []

        # Method 1: Adaptive thresholding
        thresh1 = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY, 11, 2
        )
        processed_images.append(thresh1)

        # Method 2: Otsu's thresholding
        _, thresh2 = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        processed_images.append(thresh2)

        # Method 3: Simple thresholding
        _, thresh3 = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
        processed_images.append(thresh3)

        # Method 4: Enhanced contrast
        enhanced = cv2.equalizeHist(gray)
        _, thresh4 = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        processed_images.append(thresh4)

        # Apply noise removal to all methods
        denoised_images = []
        for img in processed_images:
            try:
                denoised = cv2.fastNlMeansDenoising(img)
                denoised_images.append(denoised)
            except:
                denoised_images.append(img)

        # Return the first successfully processed image
        # In practice, you might want to test OCR on all and pick the best result
        return denoised_images[0]
    
    def clean_text(self, text):
        """Clean and normalize text for better comparison"""
        if not text:
            return ""

        # Normalize Unicode characters
        text = unicodedata.normalize('NFKC', text)

        if self.is_arabic:
            # Arabic text processing
            # Remove diacritics (tashkeel)
            text = re.sub(r'[\u064B-\u065F\u0670\u06D6-\u06ED]', '', text)

            # Normalize Arabic letters
            text = text.replace('أ', 'ا').replace('إ', 'ا').replace('آ', 'ا')
            text = text.replace('ة', 'ه')
            text = text.replace('ى', 'ي')

            # Remove extra spaces and punctuation
            text = re.sub(r'[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\w]', '', text)
        else:
            # English text processing
            text = text.lower()
            # Remove special characters and extra spaces
            text = re.sub(r'[^\w\s]', '', text)

        # Common cleanup for both languages
        text = re.sub(r'\s+', ' ', text)

        return text.strip()
    
    def extract_text(self, image_path):
        """Extract text from image using OCR"""
        try:
            print(f"Processing image: {image_path}")

            # Check if file exists
            if not os.path.exists(image_path):
                print(f"Error: Image file not found: {image_path}")
                return ""

            # Preprocess image
            processed_img = self.preprocess_image(image_path)

            # Perform OCR with different configurations
            configs = [
                '--psm 6',  # Assume uniform text block
                '--psm 4',  # Assume single column of text
                '--psm 3'   # Fully automatic page segmentation
            ]

            texts = []
            for config in configs:
                try:
                    print(f"Trying OCR with config: {config}")
                    text = pytesseract.image_to_string(
                        processed_img,
                        lang=self.language,
                        config=config
                    )
                    cleaned_text = self.clean_text(text)
                    texts.append(cleaned_text)
                    print(f"Extracted text (length {len(cleaned_text)}): {cleaned_text[:100]}...")
                except Exception as config_error:
                    print(f"OCR config {config} failed: {config_error}")
                    continue

            if not texts:
                print("No text extracted with any configuration")
                return ""

            # Return the longest text (usually the most complete)
            best_text = max(texts, key=len)
            print(f"Best extracted text: {best_text}")
            return best_text

        except Exception as e:
            print(f"Error in OCR: {str(e)}")
            import traceback
            traceback.print_exc()
            return ""
    
    def calculate_similarity(self, text1, text2):
        """Calculate similarity between two texts using multiple methods"""
        # Clean texts
        text1 = self.clean_text(text1)
        text2 = self.clean_text(text2)

        if not text1 or not text2:
            return 0.0

        # Exact match
        if text1 == text2:
            return 1.0

        # Sequence matcher similarity
        sequence_similarity = SequenceMatcher(None, text1, text2).ratio()

        # Fuzzy string matching (works well for both English and Arabic)
        fuzzy_ratio = fuzz.ratio(text1, text2) / 100.0
        fuzzy_partial = fuzz.partial_ratio(text1, text2) / 100.0
        fuzzy_token_sort = fuzz.token_sort_ratio(text1, text2) / 100.0
        fuzzy_token_set = fuzz.token_set_ratio(text1, text2) / 100.0

        # Word-based similarity
        words1 = set(text1.split())
        words2 = set(text2.split())
        if len(words1) == 0 and len(words2) == 0:
            word_similarity = 1.0
        elif len(words1) == 0 or len(words2) == 0:
            word_similarity = 0.0
        else:
            word_similarity = len(words1.intersection(words2)) / max(len(words1), len(words2))

        # Character-level similarity for Arabic
        if self.is_arabic:
            char_similarity = self._calculate_character_similarity(text1, text2)
            # Weighted combination for Arabic
            return (0.3 * sequence_similarity +
                   0.2 * fuzzy_ratio +
                   0.2 * fuzzy_token_sort +
                   0.15 * word_similarity +
                   0.15 * char_similarity)
        else:
            # Weighted combination for English
            return (0.25 * sequence_similarity +
                   0.25 * fuzzy_ratio +
                   0.2 * fuzzy_token_sort +
                   0.15 * fuzzy_partial +
                   0.15 * word_similarity)

    def _calculate_character_similarity(self, text1, text2):
        """Calculate character-level similarity (useful for Arabic)"""
        if not text1 or not text2:
            return 0.0

        # Remove spaces for character comparison
        chars1 = text1.replace(' ', '')
        chars2 = text2.replace(' ', '')

        if not chars1 or not chars2:
            return 0.0

        # Use sequence matcher on characters
        return SequenceMatcher(None, chars1, chars2).ratio()
    
    def compare_answers(self, student_answer, answer_key):
        """Compare student answer with answer key using fuzzy matching"""
        print(f"\n=== COMPARING ANSWERS ===")
        print(f"Student answer path: {student_answer}")
        print(f"Answer key path: {answer_key}")

        # Extract text from both images
        print("\n--- Extracting student text ---")
        student_text = self.extract_text(student_answer)
        print(f"Student text result: '{student_text}'")

        print("\n--- Extracting answer key text ---")
        key_text = self.extract_text(answer_key)
        print(f"Answer key text result: '{key_text}'")

        # Calculate similarity
        print(f"\n--- Calculating similarity ---")
        similarity = self.calculate_similarity(student_text, key_text)
        print(f"Similarity score: {similarity}")

        # Consider it a match if similarity is above threshold
        # Lower threshold for more lenient matching (OCR isn't perfect)
        threshold = 0.6  # Reduced from 0.8 to 0.6
        matches = similarity >= threshold
        print(f"Matches (threshold {threshold}): {matches}")

        result = {
            'matches': matches,
            'student_text': student_text,
            'key_text': key_text,
            'similarity': similarity
        }

        print(f"Final result: {result}")
        print("=== END COMPARISON ===\n")

        return result
    
    def process_correction(self, student_answer_path, answer_key_path):
        """Process a complete correction"""
        try:
            result = self.compare_answers(student_answer_path, answer_key_path)
            
            # Calculate score based on similarity
            score = result['similarity']
            
            return {
                'score': score,
                'student_text': result['student_text'],
                'key_text': result['key_text'],
                'matches': result['matches'],
                'similarity': result['similarity']
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'score': 0.0
            } 