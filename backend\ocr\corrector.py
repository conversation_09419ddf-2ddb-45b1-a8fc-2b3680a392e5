import pytesseract
from PIL import Image
import cv2
import numpy as np
import os
from difflib import SequenceMatcher
import re
from fuzzywuzzy import fuzz
import unicodedata

class AnswerCorrector:
    def __init__(self, language='eng'):
        self.language = language
        self.is_arabic = language == 'ara'
        # Configure Tesseract path if needed
        # pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
    
    def preprocess_image(self, image_path):
        """Preprocess image for better OCR results"""
        # Read image
        img = cv2.imread(image_path)
        if img is None:
            raise ValueError(f"Could not read image at {image_path}")
        
        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Apply adaptive thresholding
        thresh = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
        
        # Noise removal
        denoised = cv2.fastNlMeansDenoising(thresh)
        
        # Deskew if needed
        coords = np.column_stack(np.where(denoised > 0))
        angle = cv2.minAreaRect(coords)[-1]
        if angle < -45:
            angle = 90 + angle
        (h, w) = denoised.shape[:2]
        center = (w // 2, h // 2)
        M = cv2.getRotationMatrix2D(center, angle, 1.0)
        rotated = cv2.warpAffine(denoised, M, (w, h), flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE)
        
        return rotated
    
    def clean_text(self, text):
        """Clean and normalize text for better comparison"""
        if not text:
            return ""

        # Normalize Unicode characters
        text = unicodedata.normalize('NFKC', text)

        if self.is_arabic:
            # Arabic text processing
            # Remove diacritics (tashkeel)
            text = re.sub(r'[\u064B-\u065F\u0670\u06D6-\u06ED]', '', text)

            # Normalize Arabic letters
            text = text.replace('أ', 'ا').replace('إ', 'ا').replace('آ', 'ا')
            text = text.replace('ة', 'ه')
            text = text.replace('ى', 'ي')

            # Remove extra spaces and punctuation
            text = re.sub(r'[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\w]', '', text)
        else:
            # English text processing
            text = text.lower()
            # Remove special characters and extra spaces
            text = re.sub(r'[^\w\s]', '', text)

        # Common cleanup for both languages
        text = re.sub(r'\s+', ' ', text)

        return text.strip()
    
    def extract_text(self, image_path):
        """Extract text from image using OCR"""
        try:
            # Preprocess image
            processed_img = self.preprocess_image(image_path)
            
            # Perform OCR with different configurations
            configs = [
                '--psm 6',  # Assume uniform text block
                '--psm 4',  # Assume single column of text
                '--psm 3'   # Fully automatic page segmentation
            ]
            
            texts = []
            for config in configs:
                text = pytesseract.image_to_string(
                    processed_img,
                    lang=self.language,
                    config=config
                )
                texts.append(self.clean_text(text))
            
            # Return the longest text (usually the most complete)
            return max(texts, key=len)
            
        except Exception as e:
            print(f"Error in OCR: {str(e)}")
            return ""
    
    def calculate_similarity(self, text1, text2):
        """Calculate similarity between two texts using multiple methods"""
        # Clean texts
        text1 = self.clean_text(text1)
        text2 = self.clean_text(text2)

        if not text1 or not text2:
            return 0.0

        # Exact match
        if text1 == text2:
            return 1.0

        # Sequence matcher similarity
        sequence_similarity = SequenceMatcher(None, text1, text2).ratio()

        # Fuzzy string matching (works well for both English and Arabic)
        fuzzy_ratio = fuzz.ratio(text1, text2) / 100.0
        fuzzy_partial = fuzz.partial_ratio(text1, text2) / 100.0
        fuzzy_token_sort = fuzz.token_sort_ratio(text1, text2) / 100.0
        fuzzy_token_set = fuzz.token_set_ratio(text1, text2) / 100.0

        # Word-based similarity
        words1 = set(text1.split())
        words2 = set(text2.split())
        if len(words1) == 0 and len(words2) == 0:
            word_similarity = 1.0
        elif len(words1) == 0 or len(words2) == 0:
            word_similarity = 0.0
        else:
            word_similarity = len(words1.intersection(words2)) / max(len(words1), len(words2))

        # Character-level similarity for Arabic
        if self.is_arabic:
            char_similarity = self._calculate_character_similarity(text1, text2)
            # Weighted combination for Arabic
            return (0.3 * sequence_similarity +
                   0.2 * fuzzy_ratio +
                   0.2 * fuzzy_token_sort +
                   0.15 * word_similarity +
                   0.15 * char_similarity)
        else:
            # Weighted combination for English
            return (0.25 * sequence_similarity +
                   0.25 * fuzzy_ratio +
                   0.2 * fuzzy_token_sort +
                   0.15 * fuzzy_partial +
                   0.15 * word_similarity)

    def _calculate_character_similarity(self, text1, text2):
        """Calculate character-level similarity (useful for Arabic)"""
        if not text1 or not text2:
            return 0.0

        # Remove spaces for character comparison
        chars1 = text1.replace(' ', '')
        chars2 = text2.replace(' ', '')

        if not chars1 or not chars2:
            return 0.0

        # Use sequence matcher on characters
        return SequenceMatcher(None, chars1, chars2).ratio()
    
    def compare_answers(self, student_answer, answer_key):
        """Compare student answer with answer key using fuzzy matching"""
        # Extract text from both images
        student_text = self.extract_text(student_answer)
        key_text = self.extract_text(answer_key)
        
        # Calculate similarity
        similarity = self.calculate_similarity(student_text, key_text)
        
        # Consider it a match if similarity is above threshold
        matches = similarity >= 0.8
        
        return {
            'matches': matches,
            'student_text': student_text,
            'key_text': key_text,
            'similarity': similarity
        }
    
    def process_correction(self, student_answer_path, answer_key_path):
        """Process a complete correction"""
        try:
            result = self.compare_answers(student_answer_path, answer_key_path)
            
            # Calculate score based on similarity
            score = result['similarity']
            
            return {
                'score': score,
                'student_text': result['student_text'],
                'key_text': result['key_text'],
                'matches': result['matches'],
                'similarity': result['similarity']
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'score': 0.0
            } 