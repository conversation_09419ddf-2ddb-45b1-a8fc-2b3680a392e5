import pytesseract
from PIL import Image
import cv2
import numpy as np
import os
from difflib import SequenceMatcher
import re
import unicodedata
from fuzzywuzzy import fuzz

class AnswerCorrector:
    def __init__(self, language='eng'):
        self.language = language
        self.is_arabic = language == 'ara'

        # Configure Tesseract path for Windows
        import platform
        if platform.system() == 'Windows':
            # Try common Tesseract installation paths
            possible_paths = [
                r'C:\Program Files\Tesseract-OCR\tesseract.exe',
                r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
                r'C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe'.format(os.getenv('USERNAME', '')),
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    pytesseract.pytesseract.tesseract_cmd = path
                    print(f"Tesseract found at: {path}")
                    break
            else:
                print("Warning: Tesseract not found in common locations. Please install Tesseract OCR.")
                print("Download from: https://github.com/UB-Mannheim/tesseract/wiki")

        # Test Tesseract installation
        try:
            version = pytesseract.get_tesseract_version()
            print(f"Tesseract version: {version}")
        except Exception as e:
            print(f"Tesseract test failed: {e}")
            print("Please ensure Tesseract is properly installed and in PATH.")
    
    def preprocess_image_advanced(self, image_path):
        """Advanced image preprocessing specifically for real-world handwritten/printed text"""
        # Read image
        img = cv2.imread(image_path)
        if img is None:
            raise ValueError(f"Could not read image at {image_path}")

        print(f"Original image shape: {img.shape}")

        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Resize for optimal OCR (height should be around 600-1000px)
        height, width = gray.shape
        if height < 400:
            scale_factor = 600 / height
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            print(f"Upscaled to: {new_height}x{new_width}")
        elif height > 1500:
            scale_factor = 1000 / height
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_AREA)
            print(f"Downscaled to: {new_height}x{new_width}")

        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)

        # Enhance contrast using CLAHE
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(blurred)

        # Apply sharpening filter
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(enhanced, -1, kernel)

        # Threshold using Otsu's method
        _, binary = cv2.threshold(sharpened, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # Morphological operations to clean up
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

        # Remove small noise
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 1))
        final = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)

        return final

    def preprocess_image(self, image_path):
        """Advanced image preprocessing for better OCR results across different conditions"""
        # Read image
        img = cv2.imread(image_path)
        if img is None:
            raise ValueError(f"Could not read image at {image_path}")

        print(f"Original image shape: {img.shape}")

        # Convert to different color spaces for analysis
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        lab = cv2.cvtColor(img, cv2.COLOR_BGR2LAB)

        # Use LAB color space L channel for better text extraction
        lab_l = lab[:,:,0]

        # Resize image if too small or too large
        height, width = gray.shape
        target_height = 800  # Optimal height for OCR

        if height < 300 or height > 1200:
            scale_factor = target_height / height
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            lab_l = cv2.resize(lab_l, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            print(f"Resized to: {new_height}x{new_width}")

        # Try the advanced preprocessing first
        try:
            advanced_result = self.preprocess_image_advanced(image_path)
            return advanced_result
        except Exception as e:
            print(f"Advanced preprocessing failed: {e}, falling back to standard methods")

        # Apply multiple advanced preprocessing techniques
        processed_images = []

        # Method 1: Adaptive thresholding on grayscale
        adaptive1 = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY, 11, 2
        )
        processed_images.append(("Adaptive Gaussian", adaptive1))

        # Method 2: Adaptive thresholding with different parameters
        adaptive2 = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_MEAN_C,
            cv2.THRESH_BINARY, 15, 8
        )
        processed_images.append(("Adaptive Mean", adaptive2))

        # Method 3: Otsu's thresholding on LAB L channel
        _, otsu_lab = cv2.threshold(lab_l, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        processed_images.append(("Otsu LAB", otsu_lab))

        # Method 4: Enhanced contrast + Otsu
        enhanced = cv2.equalizeHist(gray)
        _, otsu_enhanced = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        processed_images.append(("Enhanced Otsu", otsu_enhanced))

        # Method 5: CLAHE (Contrast Limited Adaptive Histogram Equalization) + Otsu
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        clahe_img = clahe.apply(gray)
        _, clahe_otsu = cv2.threshold(clahe_img, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        processed_images.append(("CLAHE Otsu", clahe_otsu))

        # Apply noise removal and morphological operations
        final_images = []
        for name, img_proc in processed_images:
            try:
                # Noise removal
                denoised = cv2.fastNlMeansDenoising(img_proc)

                # Morphological operations to connect broken characters
                kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 1))
                opened = cv2.morphologyEx(denoised, cv2.MORPH_OPEN, kernel)

                # Dilation to make text thicker (helps with thin fonts)
                kernel2 = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 1))
                dilated = cv2.dilate(opened, kernel2, iterations=1)

                final_images.append((name, dilated))
                print(f"Processed with method: {name}")

            except Exception as e:
                print(f"Error in {name} processing: {e}")
                final_images.append((name, img_proc))

        # Return the best processed image (for now, return CLAHE Otsu as it's often best)
        best_method = "CLAHE Otsu"
        for name, img_proc in final_images:
            if name == best_method:
                print(f"Using {best_method} preprocessing")
                return img_proc

        # Fallback to first image if best method not found
        print(f"Using fallback preprocessing: {final_images[0][0]}")
        return final_images[0][1]
    
    def clean_text(self, text):
        """Clean and normalize text for better comparison"""
        if not text:
            return ""

        # Normalize Unicode characters
        text = unicodedata.normalize('NFKC', text)

        if self.is_arabic:
            # Arabic text processing
            # Remove diacritics (tashkeel)
            text = re.sub(r'[\u064B-\u065F\u0670\u06D6-\u06ED]', '', text)

            # Normalize Arabic letters
            text = text.replace('أ', 'ا').replace('إ', 'ا').replace('آ', 'ا')
            text = text.replace('ة', 'ه')
            text = text.replace('ى', 'ي')

            # Remove extra spaces and punctuation
            text = re.sub(r'[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\w]', '', text)
        else:
            # English text processing
            text = text.lower()
            # Remove special characters and extra spaces
            text = re.sub(r'[^\w\s]', '', text)

        # Common cleanup for both languages
        text = re.sub(r'\s+', ' ', text)

        return text.strip()

    def extract_key_content(self, text):
        """Extract key content (numbers, important words) from text for smart comparison"""
        if not text:
            return ""

        # Convert to lowercase for consistent comparison
        text = text.lower().strip()

        # Extract numbers (very important for math answers)
        numbers = re.findall(r'\d+(?:\.\d+)?', text)

        # Extract important words (remove common filler words)
        filler_words = {
            'is', 'the', 'answer', 'equals', 'equal', 'to', 'result', 'solution',
            'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'by', 'for', 'with',
            'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
            'above', 'below', 'between', 'among', 'this', 'that', 'these', 'those',
            'it', 'its', 'of', 'as', 'are', 'was', 'were', 'be', 'been', 'being',
            'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'must', 'can', 'shall'
        }

        # Split into words and filter out filler words
        words = text.split()
        important_words = [word for word in words if word not in filler_words and len(word) > 1]

        # Combine numbers and important words
        key_content = numbers + important_words

        # Join with spaces
        result = ' '.join(key_content)

        print(f"Original text: '{text}'")
        print(f"Extracted numbers: {numbers}")
        print(f"Important words: {important_words}")
        print(f"Key content: '{result}'")

        return result

    def extract_text_with_multiple_methods(self, image_path):
        """Extract text using multiple preprocessing methods and pick the best result"""
        # Read original image
        img = cv2.imread(image_path)
        if img is None:
            raise ValueError(f"Could not read image at {image_path}")

        # Convert to different color spaces
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        lab = cv2.cvtColor(img, cv2.COLOR_BGR2LAB)

        # Resize if needed
        height, width = gray.shape
        if height < 300 or height > 1200:
            target_height = 800
            scale_factor = target_height / height
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            lab = cv2.resize(lab, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

        # Multiple preprocessing methods
        preprocessing_methods = []

        # Method 1: CLAHE + Otsu (best for most cases)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        clahe_img = clahe.apply(gray)
        _, method1 = cv2.threshold(clahe_img, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        preprocessing_methods.append(("CLAHE_Otsu", method1))

        # Method 2: LAB L channel + Otsu (good for colored backgrounds)
        lab_l = lab[:,:,0]
        _, method2 = cv2.threshold(lab_l, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        preprocessing_methods.append(("LAB_Otsu", method2))

        # Method 3: Adaptive Gaussian (good for varying lighting)
        method3 = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
        preprocessing_methods.append(("Adaptive_Gaussian", method3))

        # Method 4: HSV-based thresholding (good for colored text)
        hsv_thresh = cv2.inRange(hsv, (0, 0, 0), (180, 255, 200))
        method4 = cv2.bitwise_not(hsv_thresh)
        preprocessing_methods.append(("HSV_Threshold", method4))

        # OCR configurations to try
        ocr_configs = [
            '--psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz ',
            '--psm 4',
            '--psm 3',
            '--psm 6',
            '--psm 8',  # Single word
        ]

        all_results = []

        # Try each preprocessing method with each OCR config
        for prep_name, processed_img in preprocessing_methods:
            for config in ocr_configs:
                try:
                    # Apply noise removal
                    denoised = cv2.fastNlMeansDenoising(processed_img)

                    # Extract text
                    text = pytesseract.image_to_string(denoised, lang=self.language, config=config)
                    cleaned_text = self.clean_text(text)

                    if cleaned_text and len(cleaned_text.strip()) > 0:
                        confidence_score = len(cleaned_text) * 0.1  # Simple confidence based on length
                        all_results.append({
                            'text': cleaned_text,
                            'method': f"{prep_name}_{config.split()[1] if len(config.split()) > 1 else 'default'}",
                            'confidence': confidence_score,
                            'length': len(cleaned_text)
                        })
                        print(f"Method {prep_name} + {config}: '{cleaned_text[:50]}...' (len: {len(cleaned_text)})")

                except Exception as e:
                    print(f"Error with {prep_name} + {config}: {e}")
                    continue

        if not all_results:
            print("No text extracted with any method")
            return ""

        # Sort by confidence and length, pick the best
        all_results.sort(key=lambda x: (x['confidence'], x['length']), reverse=True)
        best_result = all_results[0]

        print(f"Best result from {best_result['method']}: '{best_result['text']}'")
        return best_result['text']

    def extract_text(self, image_path):
        """Extract text from image using OCR with improved preprocessing"""
        try:
            print(f"Processing image: {image_path}")

            # Check if file exists
            if not os.path.exists(image_path):
                print(f"Error: Image file not found: {image_path}")
                return ""

            # Try multiple methods and pick the best
            best_text = self.extract_text_with_multiple_methods(image_path)

            if not best_text:
                print("Fallback: trying simple preprocessing")
                # Fallback to simple preprocessing
                processed_img = self.preprocess_image(image_path)
                text = pytesseract.image_to_string(processed_img, lang=self.language, config='--psm 6')
                best_text = self.clean_text(text)

            print(f"Final extracted text: '{best_text}'")
            return best_text

        except Exception as e:
            print(f"Error in OCR: {str(e)}")
            import traceback
            traceback.print_exc()
            return ""
    
    def calculate_similarity(self, text1, text2):
        """Calculate similarity between two texts using multiple methods with smart content extraction"""
        # Clean texts
        text1 = self.clean_text(text1)
        text2 = self.clean_text(text2)

        print(f"Comparing cleaned texts:")
        print(f"Text 1: '{text1}' (length: {len(text1)})")
        print(f"Text 2: '{text2}' (length: {len(text2)})")

        if not text1 or not text2:
            print("One or both texts are empty")
            return 0.0

        # Exact match
        if text1 == text2:
            print("Texts are identical!")
            return 1.0

        # Extract key content (numbers and important words)
        print(f"\n--- Extracting key content ---")
        key_content1 = self.extract_key_content(text1)
        key_content2 = self.extract_key_content(text2)

        # Check if key contents are identical (this handles "43" vs "43 is the answer")
        if key_content1 and key_content2 and key_content1 == key_content2:
            print("Key contents are identical! This is a match.")
            return 1.0

        # Calculate similarity on original text
        print(f"\n--- Original text similarity ---")
        sequence_similarity = SequenceMatcher(None, text1, text2).ratio()
        fuzzy_ratio = fuzz.ratio(text1, text2) / 100.0
        fuzzy_partial = fuzz.partial_ratio(text1, text2) / 100.0
        fuzzy_token_sort = fuzz.token_sort_ratio(text1, text2) / 100.0
        fuzzy_token_set = fuzz.token_set_ratio(text1, text2) / 100.0

        # Calculate similarity on key content (more important)
        print(f"\n--- Key content similarity ---")
        key_sequence_similarity = SequenceMatcher(None, key_content1, key_content2).ratio()
        key_fuzzy_ratio = fuzz.ratio(key_content1, key_content2) / 100.0
        key_fuzzy_partial = fuzz.partial_ratio(key_content1, key_content2) / 100.0
        key_fuzzy_token_sort = fuzz.token_sort_ratio(key_content1, key_content2) / 100.0
        key_fuzzy_token_set = fuzz.token_set_ratio(key_content1, key_content2) / 100.0

        # Word-based similarity on original text
        words1 = set(text1.split())
        words2 = set(text2.split())
        print(f"Words 1: {words1}")
        print(f"Words 2: {words2}")
        print(f"Common words: {words1.intersection(words2)}")

        if len(words1) == 0 and len(words2) == 0:
            word_similarity = 1.0
        elif len(words1) == 0 or len(words2) == 0:
            word_similarity = 0.0
        else:
            word_similarity = len(words1.intersection(words2)) / max(len(words1), len(words2))

        # Word-based similarity on key content
        key_words1 = set(key_content1.split())
        key_words2 = set(key_content2.split())
        print(f"Key words 1: {key_words1}")
        print(f"Key words 2: {key_words2}")
        print(f"Common key words: {key_words1.intersection(key_words2)}")

        if len(key_words1) == 0 and len(key_words2) == 0:
            key_word_similarity = 1.0
        elif len(key_words1) == 0 or len(key_words2) == 0:
            key_word_similarity = 0.0
        else:
            key_word_similarity = len(key_words1.intersection(key_words2)) / max(len(key_words1), len(key_words2))

        # Print all similarity scores
        print(f"\nOriginal text similarity scores:")
        print(f"  Sequence: {sequence_similarity:.3f}")
        print(f"  Fuzzy Ratio: {fuzzy_ratio:.3f}")
        print(f"  Fuzzy Partial: {fuzzy_partial:.3f}")
        print(f"  Fuzzy Token Sort: {fuzzy_token_sort:.3f}")
        print(f"  Fuzzy Token Set: {fuzzy_token_set:.3f}")
        print(f"  Word-based: {word_similarity:.3f}")

        print(f"\nKey content similarity scores:")
        print(f"  Key Sequence: {key_sequence_similarity:.3f}")
        print(f"  Key Fuzzy Ratio: {key_fuzzy_ratio:.3f}")
        print(f"  Key Fuzzy Partial: {key_fuzzy_partial:.3f}")
        print(f"  Key Fuzzy Token Sort: {key_fuzzy_token_sort:.3f}")
        print(f"  Key Fuzzy Token Set: {key_fuzzy_token_set:.3f}")
        print(f"  Key Word-based: {key_word_similarity:.3f}")

        # Calculate final score with heavy weight on key content
        if self.is_arabic:
            char_similarity = self._calculate_character_similarity(text1, text2)
            key_char_similarity = self._calculate_character_similarity(key_content1, key_content2)
            print(f"  Character-based: {char_similarity:.3f}")
            print(f"  Key Character-based: {key_char_similarity:.3f}")

            # Weighted combination for Arabic (prioritize key content)
            final_score = (
                0.1 * sequence_similarity +
                0.1 * fuzzy_ratio +
                0.1 * fuzzy_token_sort +
                0.05 * word_similarity +
                0.05 * char_similarity +
                0.2 * key_sequence_similarity +
                0.15 * key_fuzzy_ratio +
                0.15 * key_fuzzy_token_sort +
                0.05 * key_word_similarity +
                0.05 * key_char_similarity
            )
        else:
            # Weighted combination for English (prioritize key content)
            final_score = (
                0.1 * sequence_similarity +
                0.1 * fuzzy_ratio +
                0.1 * fuzzy_token_sort +
                0.05 * fuzzy_partial +
                0.05 * word_similarity +
                0.25 * key_sequence_similarity +
                0.15 * key_fuzzy_ratio +
                0.1 * key_fuzzy_token_sort +
                0.05 * key_fuzzy_partial +
                0.05 * key_word_similarity
            )

        # Boost score if key content has high similarity (handles cases like "43" vs "43 is the answer")
        if key_word_similarity >= 0.8 or key_fuzzy_ratio >= 0.8:
            print(f"High key content similarity detected, boosting score")
            final_score = max(final_score, 0.85)  # Ensure high score for good key content match

        print(f"Final weighted score: {final_score:.3f}")
        return final_score

    def _calculate_character_similarity(self, text1, text2):
        """Calculate character-level similarity (useful for Arabic)"""
        if not text1 or not text2:
            return 0.0

        # Remove spaces for character comparison
        chars1 = text1.replace(' ', '')
        chars2 = text2.replace(' ', '')

        if not chars1 or not chars2:
            return 0.0

        # Use sequence matcher on characters
        return SequenceMatcher(None, chars1, chars2).ratio()
    
    def compare_answers(self, student_answer, answer_key):
        """Compare student answer with answer key using fuzzy matching"""
        print(f"\n=== COMPARING ANSWERS ===")
        print(f"Student answer path: {student_answer}")
        print(f"Answer key path: {answer_key}")

        # Extract text from both images
        print("\n--- Extracting student text ---")
        student_text = self.extract_text(student_answer)
        print(f"Student text result: '{student_text}'")

        print("\n--- Extracting answer key text ---")
        key_text = self.extract_text(answer_key)
        print(f"Answer key text result: '{key_text}'")

        # Calculate similarity
        print(f"\n--- Calculating similarity ---")
        similarity = self.calculate_similarity(student_text, key_text)
        print(f"Similarity score: {similarity}")

        # Consider it a match if similarity is above threshold
        # Very lenient threshold for OCR (which can be quite inaccurate)
        threshold = 0.4  # Reduced from 0.6 to 0.4 for more lenient matching
        matches = similarity >= threshold
        print(f"Matches (threshold {threshold}): {matches}")

        # Also provide alternative thresholds for reference
        print(f"Would match at 0.3: {similarity >= 0.3}")
        print(f"Would match at 0.5: {similarity >= 0.5}")
        print(f"Would match at 0.7: {similarity >= 0.7}")

        result = {
            'matches': matches,
            'student_text': student_text,
            'key_text': key_text,
            'similarity': similarity
        }

        print(f"Final result: {result}")
        print("=== END COMPARISON ===\n")

        return result
    
    def process_correction(self, student_answer_path, answer_key_path):
        """Process a complete correction"""
        try:
            result = self.compare_answers(student_answer_path, answer_key_path)
            
            # Calculate score based on similarity
            score = result['similarity']
            
            return {
                'score': score,
                'student_text': result['student_text'],
                'key_text': result['key_text'],
                'matches': result['matches'],
                'similarity': result['similarity']
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'score': 0.0
            } 