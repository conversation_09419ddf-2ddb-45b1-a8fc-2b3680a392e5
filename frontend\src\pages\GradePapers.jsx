import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext.jsx';
import { adminAPI, correctionsAPI } from '../services/api.js';
import {
  DocumentArrowUpIcon,
  AcademicCapIcon,
  CheckCircleIcon,
  XCircleIcon,
  DocumentTextIcon,
  PhotoIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';

const GradePapers = () => {
  const { user } = useAuth();
  const [subjects, setSubjects] = useState([]);
  const [selectedSubject, setSelectedSubject] = useState('');
  const [answerKeyFile, setAnswerKeyFile] = useState(null);
  const [studentAnswerFile, setStudentAnswerFile] = useState(null);
  const [studentName, setStudentName] = useState('');
  const [language, setLanguage] = useState('en');
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');
  const [step, setStep] = useState(1);
  const [gradedStudents, setGradedStudents] = useState([]);

  useEffect(() => {
    fetchSubjects();
    // Load graded students from localStorage
    const saved = localStorage.getItem('gradedStudents');
    if (saved) {
      setGradedStudents(JSON.parse(saved));
    }
  }, []);

  const fetchSubjects = async () => {
    try {
      const data = await adminAPI.getSubjects();
      setSubjects(data);
    } catch (error) {
      console.error('Error fetching subjects:', error);
    }
  };

  const handleFileChange = (e, type) => {
    const file = e.target.files[0];
    if (file) {
      if (type === 'answerKey') {
        setAnswerKeyFile(file);
      } else {
        setStudentAnswerFile(file);
      }
    }
  };

  const uploadAnswerKey = async () => {
    if (!answerKeyFile || !selectedSubject) return null;
    
    try {
      const response = await correctionsAPI.uploadAnswerKey(
        answerKeyFile,
        selectedSubject,
        language
      );
      return response.file_path;
    } catch (error) {
      throw new Error('Failed to upload answer key');
    }
  };

  const uploadStudentAnswer = async () => {
    if (!studentAnswerFile || !selectedSubject || !studentName) return null;
    
    try {
      const response = await correctionsAPI.uploadStudentAnswer(
        studentAnswerFile,
        selectedSubject,
        studentName,
        language
      );
      return response.file_path;
    } catch (error) {
      throw new Error('Failed to upload student answer');
    }
  };

  const processGrading = async () => {
    setLoading(true);
    setError('');
    
    try {
      // Upload files
      const answerKeyPath = await uploadAnswerKey();
      const studentAnswerPath = await uploadStudentAnswer();
      
      if (!answerKeyPath || !studentAnswerPath) {
        throw new Error('Failed to upload files');
      }

      // Process correction
      const correctionData = {
        student_answer_path: studentAnswerPath,
        answer_key_path: answerKeyPath,
        subject_id: parseInt(selectedSubject),
        student_name: studentName,
        language: language,
        notes: notes
      };

      const result = await correctionsAPI.processCorrection(correctionData);
      const gradingResult = {
        ...result,
        date: new Date().toISOString(),
        studentName: studentName,
        subject: subjects.find(s => s.id == selectedSubject)?.name || 'Unknown',
        id: Date.now() // Simple ID generation
      };

      setResult(gradingResult);

      // Save to graded students list
      const updatedGradedStudents = [...gradedStudents, gradingResult];
      setGradedStudents(updatedGradedStudents);
      localStorage.setItem('gradedStudents', JSON.stringify(updatedGradedStudents));

      setStep(3);
    } catch (error) {
      setError(error.message || 'Failed to process grading');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setAnswerKeyFile(null);
    setStudentAnswerFile(null);
    setStudentName('');
    setNotes('');
    setResult(null);
    setError('');
    setStep(1);
  };

  const getScoreColor = (score) => {
    if (score >= 0.8) return 'text-success-600';
    if (score >= 0.6) return 'text-warning-600';
    return 'text-danger-600';
  };

  const getScoreBadge = (score) => {
    if (score >= 0.8) return 'status-badge-success';
    if (score >= 0.6) return 'status-badge-warning';
    return 'status-badge-danger';
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="page-header">
        <h1 className="page-title">Grade Papers</h1>
        <p className="mt-2 text-gray-600">
          Upload answer key and student submissions for AI-powered grading
        </p>
      </div>

      {/* Progress Steps */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-soft border border-gray-200/50 p-6">
        <div className="flex items-center justify-between">
          <div className={`flex items-center ${step >= 1 ? 'text-primary-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 1 ? 'bg-primary-600 text-white' : 'bg-gray-200'}`}>
              1
            </div>
            <span className="ml-2 font-medium">Upload Files</span>
          </div>
          <div className={`flex items-center ${step >= 2 ? 'text-primary-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 2 ? 'bg-primary-600 text-white' : 'bg-gray-200'}`}>
              2
            </div>
            <span className="ml-2 font-medium">Process</span>
          </div>
          <div className={`flex items-center ${step >= 3 ? 'text-primary-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 3 ? 'bg-primary-600 text-white' : 'bg-gray-200'}`}>
              3
            </div>
            <span className="ml-2 font-medium">Results</span>
          </div>
        </div>
      </div>

      {error && (
        <div className="rounded-xl bg-gradient-to-r from-danger-50 to-danger-100 border border-danger-200 p-4">
          <div className="flex">
            <XCircleIcon className="h-5 w-5 text-danger-400" />
            <div className="ml-3">
              <p className="text-sm text-danger-700 font-medium">{error}</p>
            </div>
          </div>
        </div>
      )}

      {step === 1 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Upload Form */}
          <div className="card">
            <h2 className="section-title">Upload Files</h2>
            
            {/* Subject Selection */}
            <div className="mb-6">
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Subject
              </label>
              <select
                value={selectedSubject}
                onChange={(e) => setSelectedSubject(e.target.value)}
                className="input-field"
                required
              >
                <option value="">Select a subject</option>
                {subjects.map((subject) => (
                  <option key={subject.id} value={subject.id}>
                    {subject.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Language Selection */}
            <div className="mb-6">
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Language
              </label>
              <select
                value={language}
                onChange={(e) => setLanguage(e.target.value)}
                className="input-field"
              >
                <option value="en">English</option>
                <option value="ar">Arabic</option>
              </select>
            </div>

            {/* Answer Key Upload */}
            <div className="mb-6">
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Answer Key
              </label>
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-xl hover:border-primary-400 transition-colors duration-200">
                <div className="space-y-1 text-center">
                  <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="flex text-sm text-gray-600">
                    <label className="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500">
                      <span>Upload answer key</span>
                      <input
                        type="file"
                        className="sr-only"
                        accept="image/*,.pdf"
                        onChange={(e) => handleFileChange(e, 'answerKey')}
                      />
                    </label>
                  </div>
                  <p className="text-xs text-gray-500">PNG, JPG, PDF up to 16MB</p>
                  {answerKeyFile && (
                    <p className="text-sm text-success-600 font-medium">
                      ✓ {answerKeyFile.name}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Student Answer Upload */}
            <div className="mb-6">
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Student Answer
              </label>
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-xl hover:border-primary-400 transition-colors duration-200">
                <div className="space-y-1 text-center">
                  <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="flex text-sm text-gray-600">
                    <label className="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500">
                      <span>Upload student answer</span>
                      <input
                        type="file"
                        className="sr-only"
                        accept="image/*,.pdf"
                        onChange={(e) => handleFileChange(e, 'studentAnswer')}
                      />
                    </label>
                  </div>
                  <p className="text-xs text-gray-500">PNG, JPG, PDF up to 16MB</p>
                  {studentAnswerFile && (
                    <p className="text-sm text-success-600 font-medium">
                      ✓ {studentAnswerFile.name}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Student Name */}
            <div className="mb-6">
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Student Name
              </label>
              <input
                type="text"
                value={studentName}
                onChange={(e) => setStudentName(e.target.value)}
                className="input-field"
                placeholder="Enter student name"
                required
              />
            </div>

            {/* Notes */}
            <div className="mb-6">
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Notes (Optional)
              </label>
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                className="input-field"
                rows={3}
                placeholder="Add any additional notes..."
              />
            </div>

            <button
              onClick={() => {
                if (answerKeyFile && studentAnswerFile && selectedSubject && studentName) {
                  setStep(2);
                  processGrading();
                }
              }}
              disabled={!answerKeyFile || !studentAnswerFile || !selectedSubject || !studentName || loading}
              className="btn-primary w-full"
            >
              {loading ? (
                <>
                  <ClockIcon className="h-5 w-5 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <AcademicCapIcon className="h-5 w-5 mr-2" />
                  Start Grading
                </>
              )}
            </button>
          </div>

          {/* Instructions */}
          <div className="card">
            <h2 className="section-title">Instructions</h2>
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="text-primary-600 font-bold text-sm">1</span>
                  </div>
                </div>
                <div className="ml-3">
                  <h3 className="font-semibold text-gray-900">Select Subject</h3>
                  <p className="text-gray-600 text-sm">Choose the subject for this grading session.</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="text-primary-600 font-bold text-sm">2</span>
                  </div>
                </div>
                <div className="ml-3">
                  <h3 className="font-semibold text-gray-900">Upload Answer Key</h3>
                  <p className="text-gray-600 text-sm">Upload the correct answer sheet as an image or PDF.</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="text-primary-600 font-bold text-sm">3</span>
                  </div>
                </div>
                <div className="ml-3">
                  <h3 className="font-semibold text-gray-900">Upload Student Answer</h3>
                  <p className="text-gray-600 text-sm">Upload the student's answer sheet for comparison.</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="text-primary-600 font-bold text-sm">4</span>
                  </div>
                </div>
                <div className="ml-3">
                  <h3 className="font-semibold text-gray-900">AI Processing</h3>
                  <p className="text-gray-600 text-sm">Our AI will analyze both documents and provide a similarity score.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {step === 2 && loading && (
        <div className="card text-center">
          <div className="animate-spin mx-auto h-12 w-12 text-primary-600 mb-4">
            <ClockIcon />
          </div>
          <h2 className="text-xl font-bold text-gray-900 mb-2">Processing...</h2>
          <p className="text-gray-600">
            Our AI is analyzing the documents and comparing the answers. This may take a few moments.
          </p>
        </div>
      )}

      {step === 3 && result && (
        <div className="space-y-6">
          {/* Results Summary */}
          <div className="card">
            <div className="flex items-center justify-between mb-6">
              <h2 className="section-title">Grading Results</h2>
              <span className={`${getScoreBadge(result.score)} text-lg font-bold`}>
                {Math.round(result.score * 100)}%
              </span>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900 mb-2">
                  {Math.round(result.similarity * 100)}%
                </div>
                <div className="text-sm text-gray-600">Similarity Score</div>
              </div>
              
              <div className="text-center">
                <div className={`text-3xl font-bold mb-2 ${result.matches ? 'text-success-600' : 'text-danger-600'}`}>
                  {result.matches ? 'PASS' : 'FAIL'}
                </div>
                <div className="text-sm text-gray-600">Result</div>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900 mb-2">
                  {new Date(result.date).toLocaleDateString()}
                </div>
                <div className="text-sm text-gray-600">Date</div>
              </div>
            </div>
          </div>

          {/* Text Comparison */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card">
              <h3 className="font-bold text-gray-900 mb-4">Answer Key Text</h3>
              <div className="bg-gray-50 rounded-lg p-4 text-sm">
                {result.key_text || 'No text extracted'}
              </div>
            </div>
            
            <div className="card">
              <h3 className="font-bold text-gray-900 mb-4">Student Answer Text</h3>
              <div className="bg-gray-50 rounded-lg p-4 text-sm">
                {result.student_text || 'No text extracted'}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-center space-x-4">
            <button
              onClick={resetForm}
              className="btn-secondary"
            >
              Grade Another Paper
            </button>
            <button
              onClick={() => {
                // TODO: Implement report generation
                alert('Report generation will be implemented');
              }}
              className="btn-primary"
            >
              Generate Report
            </button>
          </div>
        </div>
      )}

      {/* Graded Students List */}
      {gradedStudents.length > 0 && (
        <div className="card">
          <div className="flex items-center justify-between mb-6">
            <h2 className="section-title">Recently Graded Students</h2>
            <button
              onClick={() => {
                // Generate CSV report
                const csvContent = "data:text/csv;charset=utf-8,"
                  + "Student Name,Subject,Score,Result,Date\n"
                  + gradedStudents.map(student =>
                      `${student.studentName},${student.subject},${Math.round(student.similarity * 100)}%,${student.matches ? 'PASS' : 'FAIL'},${new Date(student.date).toLocaleDateString()}`
                    ).join("\n");

                const encodedUri = encodeURI(csvContent);
                const link = document.createElement("a");
                link.setAttribute("href", encodedUri);
                link.setAttribute("download", `graded_students_${new Date().toISOString().split('T')[0]}.csv`);
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              }}
              className="btn-primary"
            >
              Download Grades
            </button>
          </div>

          <div className="overflow-x-auto">
            <table className="table">
              <thead>
                <tr>
                  <th>Student Name</th>
                  <th>Subject</th>
                  <th>Score</th>
                  <th>Result</th>
                  <th>Date</th>
                </tr>
              </thead>
              <tbody>
                {gradedStudents.slice(-10).reverse().map((student) => (
                  <tr key={student.id}>
                    <td className="font-medium">{student.studentName}</td>
                    <td>{student.subject}</td>
                    <td>
                      <span className={getScoreColor(student.similarity)}>
                        {Math.round(student.similarity * 100)}%
                      </span>
                    </td>
                    <td>
                      <span className={student.matches ? 'status-badge-success' : 'status-badge-danger'}>
                        {student.matches ? 'PASS' : 'FAIL'}
                      </span>
                    </td>
                    <td>{new Date(student.date).toLocaleDateString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {gradedStudents.length > 10 && (
            <div className="text-center mt-4">
              <p className="text-sm text-gray-500">
                Showing last 10 results. Total: {gradedStudents.length} students graded.
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default GradePapers;
