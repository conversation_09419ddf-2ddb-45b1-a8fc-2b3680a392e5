from app import create_app
from models import Admin, db
from werkzeug.security import check_password_hash

def check_admin():
    app = create_app()
    with app.app_context():
        # Get all admins
        admins = Admin.query.all()
        print(f"Found {len(admins)} admin users:")
        
        for admin in admins:
            print(f"\nAdmin ID: {admin.id}")
            print(f"Name: {admin.name}")
            print(f"Email: {admin.email}")
            print(f"Password hash: {admin.password_hash}")
            
            # Test password
            test_password = "admin123"
            if check_password_hash(admin.password_hash, test_password):
                print("Password 'admin123' is correct")
            else:
                print("Password 'admin123' is incorrect")

if __name__ == '__main__':
    check_admin() 