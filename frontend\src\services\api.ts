import axios from 'axios';

// API Configuration
const API_BASE_URL = 'http://localhost:5000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Types
export interface User {
  id: number;
  name: string;
  email: string;
  role: 'admin' | 'teacher' | 'student';
}

export interface Subject {
  id: number;
  name: string;
  description: string;
}

export interface LoginResponse {
  access_token: string;
  user: User;
}

export interface Correction {
  id: number;
  score: number;
  similarity: number;
  matches: boolean;
  student_text: string;
  key_text: string;
  student_name: string;
  subject: string;
  date: string;
  language: string;
  notes?: string;
}

export interface UploadResponse {
  message: string;
  file_path: string;
  subject_id?: string;
  student_name?: string;
  language?: string;
}

export interface ProcessCorrectionRequest {
  student_answer_path: string;
  answer_key_path: string;
  subject_id: number;
  student_name: string;
  language: string;
  notes?: string;
}

export interface BatchProcessRequest {
  corrections: ProcessCorrectionRequest[];
}

export interface BatchProcessResponse {
  successful_corrections: number;
  failed_corrections: number;
  results: Correction[];
  failures: Array<{
    index: number;
    error: string;
  }>;
}

export interface ReportResponse {
  message: string;
  report_path: string;
  download_url: string;
}

// Auth API
export const authAPI = {
  login: async (email: string, password: string): Promise<LoginResponse> => {
    const response = await api.post('/auth/login', { email, password });
    return response.data;
  },

  getCurrentUser: async (): Promise<User> => {
    const response = await api.get('/auth/me');
    return response.data;
  },

  logout: () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  },
};

// Admin API
export const adminAPI = {
  getUsers: async (): Promise<User[]> => {
    const response = await api.get('/admin/users');
    return response.data;
  },

  createUser: async (userData: Omit<User, 'id'> & { password: string; subjects?: string[] }): Promise<User> => {
    const response = await api.post('/admin/users', userData);
    return response.data;
  },

  updateUser: async (id: number, userData: Partial<User> & { subjects?: string[] }): Promise<User> => {
    const response = await api.put(`/admin/users/${id}`, userData);
    return response.data;
  },

  deleteUser: async (id: number): Promise<void> => {
    await api.delete(`/admin/users/${id}`);
  },

  getSubjects: async (): Promise<Subject[]> => {
    const response = await api.get('/admin/subjects');
    return response.data;
  },

  createSubject: async (subjectData: Omit<Subject, 'id'>): Promise<Subject> => {
    const response = await api.post('/admin/subjects', subjectData);
    return response.data;
  },

  updateSubject: async (id: number, subjectData: Partial<Subject>): Promise<Subject> => {
    const response = await api.put(`/admin/subjects/${id}`, subjectData);
    return response.data;
  },

  deleteSubject: async (id: number): Promise<void> => {
    await api.delete(`/admin/subjects/${id}`);
  },

  getDebugInfo: async (): Promise<any> => {
    const response = await api.get('/admin/debug/db');
    return response.data;
  },
};

// Corrections API
export const correctionsAPI = {
  uploadAnswerKey: async (file: File, subjectId: number, language: string = 'en'): Promise<UploadResponse> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('subject_id', subjectId.toString());
    formData.append('language', language);

    const response = await api.post('/corrections/upload/answer-key', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  uploadStudentAnswer: async (
    file: File,
    subjectId: number,
    studentName: string,
    language: string = 'en'
  ): Promise<UploadResponse> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('subject_id', subjectId.toString());
    formData.append('student_name', studentName);
    formData.append('language', language);

    const response = await api.post('/corrections/upload/student-answer', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  uploadSignature: async (file: File): Promise<UploadResponse> => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/corrections/upload/signature', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  processCorrection: async (data: ProcessCorrectionRequest): Promise<Correction> => {
    const response = await api.post('/corrections/process', data);
    return response.data;
  },

  batchProcess: async (data: BatchProcessRequest): Promise<BatchProcessResponse> => {
    const response = await api.post('/corrections/batch-process', data);
    return response.data;
  },

  generateReport: async (correctionId: number, studentName?: string, signaturePath?: string): Promise<ReportResponse> => {
    const response = await api.post(`/corrections/generate-report/${correctionId}`, {
      student_name: studentName,
      signature_path: signaturePath,
    });
    return response.data;
  },

  generateBatchReport: async (correctionIds: number[], signaturePath?: string): Promise<ReportResponse> => {
    const response = await api.post('/corrections/generate-batch-report', {
      correction_ids: correctionIds,
      signature_path: signaturePath,
    });
    return response.data;
  },

  downloadReport: async (filename: string): Promise<Blob> => {
    const response = await api.get(`/corrections/download-report/${filename}`, {
      responseType: 'blob',
    });
    return response.data;
  },
};

export default api;
