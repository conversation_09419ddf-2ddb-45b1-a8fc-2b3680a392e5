from app import create_app
from models import Admin, db
from werkzeug.security import generate_password_hash

def create_admin():
    app = create_app()
    with app.app_context():
        # Check if admin already exists
        admin = Admin.query.filter_by(email='<EMAIL>').first()
        if admin:
            print('Admin user already exists')
            return

        # Create admin user
        admin = Admin(
            name='Admin',
            email='<EMAIL>',
            password_hash=generate_password_hash('admin123')
        )
        
        try:
            db.session.add(admin)
            db.session.commit()
            print('Admin user created successfully')
            print('Email: <EMAIL>')
            print('Password: admin123')
        except Exception as e:
            print(f'Error creating admin: {str(e)}')
            db.session.rollback()

if __name__ == '__main__':
    create_admin() 