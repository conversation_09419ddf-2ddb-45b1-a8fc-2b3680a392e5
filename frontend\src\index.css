@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-gradient-to-br from-gray-50 to-gray-100 text-gray-900 antialiased;
  }

  * {
    @apply border-gray-200;
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-semibold py-3 px-6 rounded-xl shadow-soft hover:shadow-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transform hover:scale-105;
  }

  .btn-secondary {
    @apply bg-white hover:bg-gray-50 text-gray-700 font-semibold py-3 px-6 rounded-xl border border-gray-200 shadow-soft hover:shadow-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transform hover:scale-105;
  }

  .btn-danger {
    @apply bg-gradient-to-r from-danger-600 to-danger-700 hover:from-danger-700 hover:to-danger-800 text-white font-semibold py-3 px-6 rounded-xl shadow-soft hover:shadow-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-danger-500 focus:ring-offset-2 transform hover:scale-105;
  }

  .btn-success {
    @apply bg-gradient-to-r from-success-600 to-success-700 hover:from-success-700 hover:to-success-800 text-white font-semibold py-3 px-6 rounded-xl shadow-soft hover:shadow-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-success-500 focus:ring-offset-2 transform hover:scale-105;
  }

  .input-field {
    @apply block w-full rounded-xl border-gray-300 shadow-soft focus:border-primary-500 focus:ring-primary-500 focus:ring-2 transition-all duration-200 bg-white/80 backdrop-blur-sm;
  }

  .card {
    @apply bg-white/80 backdrop-blur-sm rounded-2xl shadow-soft hover:shadow-medium border border-gray-200/50 p-8 transition-all duration-300 animate-fade-in;
  }

  .card-interactive {
    @apply card hover:scale-[1.02] cursor-pointer;
  }

  .page-header {
    @apply border-b border-gray-200/50 pb-6 mb-8 bg-gradient-to-r from-white/80 to-gray-50/80 backdrop-blur-sm rounded-2xl p-6 shadow-soft;
  }

  .page-title {
    @apply text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent;
  }

  .section-title {
    @apply text-xl font-bold text-gray-900 mb-6 flex items-center gap-3;
  }

  .sidebar-item {
    @apply group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 hover:scale-105;
  }

  .sidebar-item-active {
    @apply bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-medium;
  }

  .sidebar-item-inactive {
    @apply text-gray-600 hover:bg-white/80 hover:text-gray-900 hover:shadow-soft;
  }

  .stat-card {
    @apply bg-gradient-to-br from-white to-gray-50 rounded-2xl shadow-soft hover:shadow-medium border border-gray-200/50 p-6 transition-all duration-300 hover:scale-105 animate-slide-up;
  }

  .feature-card {
    @apply bg-gradient-to-br from-white via-gray-50 to-white rounded-2xl shadow-soft hover:shadow-large border border-gray-200/50 p-8 transition-all duration-500 hover:scale-105 text-center group;
  }

  .quick-action-card {
    @apply bg-gradient-to-br from-white to-gray-50 rounded-2xl shadow-soft hover:shadow-large border border-gray-200/50 p-6 transition-all duration-300 hover:scale-105 group cursor-pointer;
  }

  .status-badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold;
  }

  .status-badge-success {
    @apply status-badge bg-success-100 text-success-800 border border-success-200;
  }

  .status-badge-warning {
    @apply status-badge bg-warning-100 text-warning-800 border border-warning-200;
  }

  .status-badge-danger {
    @apply status-badge bg-danger-100 text-danger-800 border border-danger-200;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent;
  }

  .glass-effect {
    @apply bg-white/20 backdrop-blur-lg border border-white/30;
  }
}
