import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext.jsx';
import { adminAPI } from '../services/api.js';
import {
  UserGroupIcon,
  BookOpenIcon,
  DocumentTextIcon,
  ChartBarIcon,
  AcademicCapIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';

const Dashboard = () => {
  const { user, isAdmin } = useAuth();
  const [stats, setStats] = useState({
    students: 0,
    teachers: 0,
    subjects: 0,
    admins: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      if (isAdmin) {
        try {
          const debugInfo = await adminAPI.getDebugInfo();
          setStats({
            students: debugInfo.students?.length || 0,
            teachers: debugInfo.teachers?.length || 0,
            subjects: debugInfo.subjects?.length || 0,
            admins: debugInfo.admins?.length || 0,
          });
        } catch (error) {
          console.error('Error fetching stats:', error);
        }
      }
      setLoading(false);
    };

    fetchStats();
  }, [isAdmin]);

  const quickActions = [
    {
      name: 'Grade Papers',
      description: 'Upload and grade student answers',
      href: '/grade',
      icon: DocumentTextIcon,
      color: 'bg-blue-500',
      show: user?.role === 'teacher' || user?.role === 'admin',
    },
    {
      name: 'Batch Grading',
      description: 'Process multiple submissions at once',
      href: '/batch',
      icon: ChartBarIcon,
      color: 'bg-green-500',
      show: user?.role === 'teacher' || user?.role === 'admin',
    },
    {
      name: 'Manage Users',
      description: 'Add and manage teachers and students',
      href: '/users',
      icon: UserGroupIcon,
      color: 'bg-purple-500',
      show: user?.role === 'admin',
    },
    {
      name: 'Manage Subjects',
      description: 'Create and organize subjects',
      href: '/subjects',
      icon: BookOpenIcon,
      color: 'bg-orange-500',
      show: user?.role === 'admin',
    },
  ].filter(action => action.show);

  const statCards = [
    {
      name: 'Total Students',
      value: stats.students,
      icon: AcademicCapIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      show: isAdmin,
    },
    {
      name: 'Total Teachers',
      value: stats.teachers,
      icon: UserGroupIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      show: isAdmin,
    },
    {
      name: 'Total Subjects',
      value: stats.subjects,
      icon: BookOpenIcon,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      show: isAdmin,
    },
    {
      name: 'Total Admins',
      value: stats.admins,
      icon: UserGroupIcon,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      show: isAdmin,
    },
  ].filter(card => card.show);

  const features = [
    {
      name: 'AI-Powered OCR',
      description: 'Advanced text recognition for English and Arabic handwriting',
      icon: AcademicCapIcon,
    },
    {
      name: 'Smart Grading',
      description: 'Intelligent similarity matching and scoring algorithms',
      icon: ChartBarIcon,
    },
    {
      name: 'PDF Reports',
      description: 'Professional grading reports with teacher signatures',
      icon: DocumentTextIcon,
    },
    {
      name: 'Batch Processing',
      description: 'Grade multiple submissions simultaneously for efficiency',
      icon: ClockIcon,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="page-header">
        <h1 className="page-title">
          Welcome back, {user?.name}!
        </h1>
        <p className="mt-2 text-sm text-gray-600">
          Here's what's happening with your AHcorrecting system today.
        </p>
      </div>

      {/* Stats Cards */}
      {isAdmin && (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {statCards.map((card, index) => (
            <div key={card.name} className="stat-card" style={{ animationDelay: `${index * 100}ms` }}>
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`p-4 rounded-2xl ${card.bgColor} shadow-soft`}>
                    <card.icon className={`h-7 w-7 ${card.color}`} />
                  </div>
                </div>
                <div className="ml-5">
                  <p className="text-sm font-semibold text-gray-600 uppercase tracking-wide">{card.name}</p>
                  <p className="text-3xl font-bold text-gray-900 mt-1">
                    {loading ? (
                      <div className="animate-pulse bg-gray-200 h-8 w-16 rounded"></div>
                    ) : (
                      card.value
                    )}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Quick Actions */}
      <div>
        <h2 className="section-title">
          <span className="gradient-text">Quick Actions</span>
        </h2>
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {quickActions.map((action, index) => (
            <a
              key={action.name}
              href={action.href}
              className="quick-action-card"
              style={{ animationDelay: `${index * 150}ms` }}
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`p-4 rounded-2xl ${action.color} shadow-medium group-hover:shadow-large transition-all duration-300`}>
                    <action.icon className="h-7 w-7 text-white" />
                  </div>
                </div>
                <div className="ml-5">
                  <h3 className="text-lg font-bold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">
                    {action.name}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">{action.description}</p>
                </div>
              </div>
            </a>
          ))}
        </div>
      </div>

      {/* Features Overview */}
      <div>
        <h2 className="section-title">
          <span className="gradient-text">System Features</span>
        </h2>
        <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
          {features.map((feature, index) => (
            <div key={feature.name} className="feature-card" style={{ animationDelay: `${index * 200}ms` }}>
              <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-2xl bg-gradient-to-r from-primary-100 to-primary-200 shadow-soft group-hover:shadow-medium transition-all duration-300">
                <feature.icon className="h-8 w-8 text-primary-600 group-hover:scale-110 transition-transform duration-300" />
              </div>
              <h3 className="mt-6 text-xl font-bold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">{feature.name}</h3>
              <p className="mt-3 text-sm text-gray-600 leading-relaxed">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Getting Started */}
      {user?.role === 'teacher' && (
        <div className="bg-gradient-to-r from-primary-50 via-primary-100 to-accent-50 rounded-2xl shadow-soft hover:shadow-medium border border-primary-200/50 p-8 transition-all duration-300 animate-fade-in">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <div className="h-12 w-12 rounded-2xl bg-gradient-to-r from-primary-500 to-primary-600 flex items-center justify-center shadow-medium">
                <AcademicCapIcon className="h-7 w-7 text-white" />
              </div>
            </div>
            <div className="ml-6">
              <h3 className="text-2xl font-bold text-primary-900 mb-3">
                Ready to start grading?
              </h3>
              <p className="text-primary-700 leading-relaxed mb-6">
                Upload your answer key and student submissions to begin the AI-powered grading process.
              </p>
              <div>
                <a
                  href="/grade"
                  className="btn-primary inline-flex items-center"
                >
                  Start Grading
                </a>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* System Status */}
      <div className="card">
        <h3 className="section-title">
          <span className="gradient-text">System Status</span>
        </h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-white rounded-xl border border-gray-200/50">
            <span className="text-sm font-semibold text-gray-700">Backend API</span>
            <span className="status-badge-success">
              Connected
            </span>
          </div>
          <div className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-white rounded-xl border border-gray-200/50">
            <span className="text-sm font-semibold text-gray-700">Database</span>
            <span className="status-badge-success">
              Online
            </span>
          </div>
          <div className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-white rounded-xl border border-gray-200/50">
            <span className="text-sm font-semibold text-gray-700">File Upload</span>
            <span className="status-badge-success">
              Ready
            </span>
          </div>
          <div className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-white rounded-xl border border-gray-200/50">
            <span className="text-sm font-semibold text-gray-700">OCR Engine</span>
            <span className="status-badge-warning">
              Requires Tesseract
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
