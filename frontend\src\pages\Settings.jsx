import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext.jsx';
import { correctionsAPI } from '../services/api.js';
import {
  Cog6ToothIcon,
  UserIcon,
  DocumentArrowUpIcon,
  LanguageIcon,
  BellIcon,
  ShieldCheckIcon,
  PhotoIcon,
  CheckIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';

const Settings = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  const [signatureFile, setSignatureFile] = useState(null);
  const [signatureUploaded, setSignatureUploaded] = useState(false);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');

  const [settings, setSettings] = useState({
    defaultLanguage: 'en',
    emailNotifications: true,
    gradeNotifications: true,
    systemNotifications: false,
    autoSave: true,
    theme: 'light',
  });

  const tabs = [
    { id: 'profile', name: 'Profile', icon: UserIcon },
    { id: 'signature', name: 'Signature', icon: DocumentArrowUpIcon },
    { id: 'preferences', name: 'Preferences', icon: Cog6ToothIcon },
    { id: 'notifications', name: 'Notifications', icon: BellIcon },
    { id: 'security', name: 'Security', icon: ShieldCheckIcon },
  ];

  const handleSignatureUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    setSignatureFile(file);
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      await correctionsAPI.uploadSignature(file);
      setSignatureUploaded(true);
      setSuccess('Signature uploaded successfully');
    } catch (error) {
      setError('Failed to upload signature');
    } finally {
      setLoading(false);
    }
  };

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const saveSettings = () => {
    // TODO: Implement settings save to backend
    setSuccess('Settings saved successfully');
    setTimeout(() => setSuccess(''), 3000);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="page-header">
        <h1 className="page-title">Settings</h1>
        <p className="mt-2 text-gray-600">
          Manage your account settings and preferences
        </p>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div className="rounded-xl bg-gradient-to-r from-success-50 to-success-100 border border-success-200 p-4">
          <div className="flex">
            <CheckIcon className="h-5 w-5 text-success-400" />
            <div className="ml-3">
              <p className="text-sm text-success-700 font-medium">{success}</p>
            </div>
          </div>
        </div>
      )}

      {error && (
        <div className="rounded-xl bg-gradient-to-r from-danger-50 to-danger-100 border border-danger-200 p-4">
          <div className="flex">
            <XMarkIcon className="h-5 w-5 text-danger-400" />
            <div className="ml-3">
              <p className="text-sm text-danger-700 font-medium">{error}</p>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <nav className="space-y-1">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-primary-100 text-primary-700 border border-primary-200'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                <tab.icon className="mr-3 h-5 w-5" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          <div className="card">
            {/* Profile Tab */}
            {activeTab === 'profile' && (
              <div>
                <h2 className="section-title">Profile Information</h2>
                <div className="space-y-6">
                  <div className="flex items-center space-x-6">
                    <div className="h-20 w-20 rounded-full bg-gradient-to-r from-primary-500 to-primary-600 flex items-center justify-center shadow-medium">
                      <span className="text-2xl font-bold text-white">
                        {user?.name?.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{user?.name}</h3>
                      <p className="text-sm text-gray-500">{user?.email}</p>
                      <p className="text-sm text-gray-500 capitalize">Role: {user?.role}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Full Name
                      </label>
                      <input
                        type="text"
                        value={user?.name || ''}
                        className="input-field"
                        disabled
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Email Address
                      </label>
                      <input
                        type="email"
                        value={user?.email || ''}
                        className="input-field"
                        disabled
                      />
                    </div>
                  </div>

                  <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
                    <p className="text-sm text-yellow-700">
                      <strong>Note:</strong> Profile information can only be updated by an administrator.
                      Contact your system administrator to make changes.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Signature Tab */}
            {activeTab === 'signature' && (
              <div>
                <h2 className="section-title">Digital Signature</h2>
                <div className="space-y-6">
                  <p className="text-gray-600">
                    Upload your digital signature to be included in grading reports.
                  </p>

                  <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-primary-400 transition-colors duration-200">
                    <PhotoIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <div className="space-y-2">
                      <label className="cursor-pointer">
                        <span className="btn-primary inline-flex items-center">
                          <DocumentArrowUpIcon className="h-5 w-5 mr-2" />
                          {signatureUploaded ? 'Replace Signature' : 'Upload Signature'}
                        </span>
                        <input
                          type="file"
                          className="sr-only"
                          accept="image/*"
                          onChange={handleSignatureUpload}
                          disabled={loading}
                        />
                      </label>
                      <p className="text-sm text-gray-500">
                        PNG, JPG up to 5MB. Recommended size: 300x100px
                      </p>
                    </div>
                  </div>

                  {signatureFile && (
                    <div className="bg-gray-50 rounded-xl p-4">
                      <p className="text-sm font-medium text-gray-900 mb-2">Selected File:</p>
                      <p className="text-sm text-gray-600">{signatureFile.name}</p>
                    </div>
                  )}

                  {signatureUploaded && (
                    <div className="bg-success-50 border border-success-200 rounded-xl p-4">
                      <p className="text-sm text-success-700">
                        ✓ Signature uploaded successfully. It will be included in future grading reports.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Preferences Tab */}
            {activeTab === 'preferences' && (
              <div>
                <h2 className="section-title">Preferences</h2>
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Default Language for OCR
                    </label>
                    <select
                      value={settings.defaultLanguage}
                      onChange={(e) => handleSettingChange('defaultLanguage', e.target.value)}
                      className="input-field"
                    >
                      <option value="en">English</option>
                      <option value="ar">Arabic</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Theme
                    </label>
                    <select
                      value={settings.theme}
                      onChange={(e) => handleSettingChange('theme', e.target.value)}
                      className="input-field"
                    >
                      <option value="light">Light</option>
                      <option value="dark">Dark</option>
                      <option value="auto">Auto</option>
                    </select>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">Auto-save drafts</h3>
                      <p className="text-sm text-gray-500">Automatically save your work as you type</p>
                    </div>
                    <button
                      onClick={() => handleSettingChange('autoSave', !settings.autoSave)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        settings.autoSave ? 'bg-primary-600' : 'bg-gray-200'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          settings.autoSave ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>

                  <div className="pt-4">
                    <button onClick={saveSettings} className="btn-primary">
                      Save Preferences
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Notifications Tab */}
            {activeTab === 'notifications' && (
              <div>
                <h2 className="section-title">Notification Settings</h2>
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">Email Notifications</h3>
                      <p className="text-sm text-gray-500">Receive email updates about your account</p>
                    </div>
                    <button
                      onClick={() => handleSettingChange('emailNotifications', !settings.emailNotifications)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        settings.emailNotifications ? 'bg-primary-600' : 'bg-gray-200'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          settings.emailNotifications ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">Grading Notifications</h3>
                      <p className="text-sm text-gray-500">Get notified when grading is complete</p>
                    </div>
                    <button
                      onClick={() => handleSettingChange('gradeNotifications', !settings.gradeNotifications)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        settings.gradeNotifications ? 'bg-primary-600' : 'bg-gray-200'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          settings.gradeNotifications ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">System Notifications</h3>
                      <p className="text-sm text-gray-500">Receive system maintenance and update notifications</p>
                    </div>
                    <button
                      onClick={() => handleSettingChange('systemNotifications', !settings.systemNotifications)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        settings.systemNotifications ? 'bg-primary-600' : 'bg-gray-200'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          settings.systemNotifications ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>

                  <div className="pt-4">
                    <button onClick={saveSettings} className="btn-primary">
                      Save Notification Settings
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Security Tab */}
            {activeTab === 'security' && (
              <div>
                <h2 className="section-title">Security Settings</h2>
                <div className="space-y-6">
                  <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                    <div className="flex">
                      <ShieldCheckIcon className="h-5 w-5 text-blue-400" />
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-blue-800">Account Security</h3>
                        <p className="text-sm text-blue-700 mt-1">
                          Your account is secured with JWT authentication. Password changes must be requested through an administrator.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900 mb-2">Current Session</h3>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="text-sm font-medium text-gray-900">Web Browser</p>
                            <p className="text-sm text-gray-500">Current session</p>
                          </div>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Active
                          </span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-gray-900 mb-2">Security Actions</h3>
                      <div className="space-y-3">
                        <button
                          onClick={() => {
                            if (window.confirm('Are you sure you want to log out?')) {
                              // This would trigger logout
                              window.location.href = '/login';
                            }
                          }}
                          className="btn-secondary w-full justify-center"
                        >
                          Log Out of This Session
                        </button>
                        
                        <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
                          <p className="text-sm text-yellow-700">
                            <strong>Need to change your password?</strong> Contact your system administrator to request a password reset.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
