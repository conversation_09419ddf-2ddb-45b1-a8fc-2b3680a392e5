import unittest
import os
import sys
import tempfile
import shutil
from PIL import Image, ImageDraw, ImageFont
import cv2
import numpy as np

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ocr.corrector import AnswerCorrector

class TestOCRCorrector(unittest.TestCase):
    """Test cases for OCR functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.corrector_en = AnswerCorrector(language='eng')
        self.corrector_ar = AnswerCorrector(language='ara')
    
    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir)
    
    def create_test_image(self, text, language='en', width=400, height=200):
        """Create a test image with text"""
        # Create a white image
        img = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(img)
        
        try:
            # Try to use a default font
            if language == 'ar':
                # For Arabic, we'd need an Arabic font, but for testing we'll use default
                font = ImageFont.load_default()
            else:
                font = ImageFont.load_default()
        except:
            font = ImageFont.load_default()
        
        # Calculate text position (center)
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        x = (width - text_width) // 2
        y = (height - text_height) // 2
        
        # Draw text
        draw.text((x, y), text, fill='black', font=font)
        
        # Save image
        image_path = os.path.join(self.temp_dir, f'test_image_{language}.png')
        img.save(image_path)
        
        return image_path
    
    def test_text_cleaning_english(self):
        """Test text cleaning for English"""
        test_cases = [
            ("Hello World!", "hello world"),
            ("  Multiple   Spaces  ", "multiple spaces"),
            ("Special@#$Characters", "specialcharacters"),
            ("123Numbers456", "123numbers456"),
            ("", "")
        ]
        
        for input_text, expected in test_cases:
            with self.subTest(input_text=input_text):
                result = self.corrector_en.clean_text(input_text)
                self.assertEqual(result, expected)
    
    def test_text_cleaning_arabic(self):
        """Test text cleaning for Arabic"""
        test_cases = [
            ("مرحبا بالعالم", "مرحبا بالعالم"),  # Basic Arabic
            ("  مرحبا   بالعالم  ", "مرحبا بالعالم"),  # Multiple spaces
            ("أهلاً وسهلاً", "اهلا وسهلا"),  # Normalize alif and remove diacritics
            ("", "")
        ]
        
        for input_text, expected in test_cases:
            with self.subTest(input_text=input_text):
                result = self.corrector_ar.clean_text(input_text)
                self.assertEqual(result, expected)
    
    def test_similarity_calculation_english(self):
        """Test similarity calculation for English text"""
        test_cases = [
            ("hello world", "hello world", 1.0),  # Exact match
            ("hello world", "hello earth", 0.5),  # Partial match
            ("", "", 1.0),  # Empty strings
            ("hello", "", 0.0),  # One empty
            ("cat", "dog", 0.0),  # No similarity
        ]
        
        for text1, text2, min_expected in test_cases:
            with self.subTest(text1=text1, text2=text2):
                result = self.corrector_en.calculate_similarity(text1, text2)
                if min_expected == 1.0:
                    self.assertEqual(result, 1.0)
                elif min_expected == 0.0 and text1 and text2:
                    self.assertGreaterEqual(result, 0.0)
                    self.assertLessEqual(result, 1.0)
                else:
                    self.assertGreaterEqual(result, min_expected)
                    self.assertLessEqual(result, 1.0)
    
    def test_similarity_calculation_arabic(self):
        """Test similarity calculation for Arabic text"""
        test_cases = [
            ("مرحبا", "مرحبا", 1.0),  # Exact match
            ("مرحبا بالعالم", "مرحبا بالكون", 0.3),  # Partial match
            ("", "", 1.0),  # Empty strings
            ("مرحبا", "", 0.0),  # One empty
        ]
        
        for text1, text2, min_expected in test_cases:
            with self.subTest(text1=text1, text2=text2):
                result = self.corrector_ar.calculate_similarity(text1, text2)
                if min_expected == 1.0:
                    self.assertEqual(result, 1.0)
                elif min_expected == 0.0 and text1 and text2:
                    self.assertGreaterEqual(result, 0.0)
                    self.assertLessEqual(result, 1.0)
                else:
                    self.assertGreaterEqual(result, min_expected)
                    self.assertLessEqual(result, 1.0)
    
    def test_character_similarity(self):
        """Test character-level similarity calculation"""
        test_cases = [
            ("abc", "abc", 1.0),
            ("abc", "ab", 0.5),
            ("abc", "xyz", 0.0),
            ("", "", 0.0),  # Both empty
            ("abc", "", 0.0),  # One empty
        ]
        
        for text1, text2, expected in test_cases:
            with self.subTest(text1=text1, text2=text2):
                result = self.corrector_ar._calculate_character_similarity(text1, text2)
                if expected == 0.0 and not text1 and not text2:
                    self.assertEqual(result, 0.0)
                else:
                    self.assertAlmostEqual(result, expected, places=1)
    
    def test_image_preprocessing(self):
        """Test image preprocessing functionality"""
        # Create a test image
        image_path = self.create_test_image("Test Text", 'en')
        
        # Test preprocessing
        try:
            processed_img = self.corrector_en.preprocess_image(image_path)
            self.assertIsNotNone(processed_img)
            self.assertIsInstance(processed_img, np.ndarray)
        except Exception as e:
            # If preprocessing fails, it might be due to missing dependencies
            self.skipTest(f"Image preprocessing failed: {str(e)}")
    
    def test_ocr_extraction_english(self):
        """Test OCR text extraction for English"""
        # Create a simple test image with English text
        image_path = self.create_test_image("Hello World", 'en')
        
        try:
            # Test text extraction
            extracted_text = self.corrector_en.extract_text(image_path)
            
            # The extracted text might not be perfect, so we check if it contains some expected words
            self.assertIsInstance(extracted_text, str)
            # Note: OCR results can vary, so we're just checking that we get some text back
            
        except Exception as e:
            # If OCR fails, it might be due to missing Tesseract installation
            self.skipTest(f"OCR extraction failed: {str(e)}")
    
    def test_compare_answers_workflow(self):
        """Test the complete answer comparison workflow"""
        # Create two similar images
        student_image = self.create_test_image("The quick brown fox", 'en')
        answer_key_image = self.create_test_image("The quick brown fox", 'en')
        
        try:
            # Test comparison
            result = self.corrector_en.compare_answers(student_image, answer_key_image)
            
            # Check result structure
            self.assertIsInstance(result, dict)
            self.assertIn('matches', result)
            self.assertIn('student_text', result)
            self.assertIn('key_text', result)
            self.assertIn('similarity', result)
            
            # Check data types
            self.assertIsInstance(result['matches'], bool)
            self.assertIsInstance(result['student_text'], str)
            self.assertIsInstance(result['key_text'], str)
            self.assertIsInstance(result['similarity'], (int, float))
            
            # Check similarity range
            self.assertGreaterEqual(result['similarity'], 0.0)
            self.assertLessEqual(result['similarity'], 1.0)
            
        except Exception as e:
            self.skipTest(f"Answer comparison failed: {str(e)}")
    
    def test_process_correction_workflow(self):
        """Test the complete correction processing workflow"""
        # Create test images
        student_image = self.create_test_image("Mathematics is fun", 'en')
        answer_key_image = self.create_test_image("Mathematics is fun", 'en')
        
        try:
            # Test processing
            result = self.corrector_en.process_correction(student_image, answer_key_image)
            
            # Check result structure
            self.assertIsInstance(result, dict)
            
            if 'error' not in result:
                # Success case
                self.assertIn('score', result)
                self.assertIn('student_text', result)
                self.assertIn('key_text', result)
                self.assertIn('matches', result)
                self.assertIn('similarity', result)
                
                # Check score range
                self.assertGreaterEqual(result['score'], 0.0)
                self.assertLessEqual(result['score'], 1.0)
            else:
                # Error case - just check that error is a string
                self.assertIsInstance(result['error'], str)
                self.assertEqual(result['score'], 0.0)
            
        except Exception as e:
            self.skipTest(f"Correction processing failed: {str(e)}")
    
    def test_invalid_image_path(self):
        """Test handling of invalid image paths"""
        invalid_path = "/nonexistent/path/image.png"
        
        # Should return empty string for invalid paths
        result = self.corrector_en.extract_text(invalid_path)
        self.assertEqual(result, "")
    
    def test_language_detection(self):
        """Test language-specific processing"""
        # Test English corrector
        self.assertEqual(self.corrector_en.language, 'eng')
        self.assertFalse(self.corrector_en.is_arabic)
        
        # Test Arabic corrector
        self.assertEqual(self.corrector_ar.language, 'ara')
        self.assertTrue(self.corrector_ar.is_arabic)

if __name__ == '__main__':
    # Run tests
    unittest.main(verbosity=2)
