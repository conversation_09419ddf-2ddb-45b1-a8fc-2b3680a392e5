@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-family: 'Inter', system-ui, sans-serif;
  scroll-behavior: smooth;
}

body {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  color: #1f2937;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Color Variables */
:root {
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;
  
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  
  --success-50: #f0fdf4;
  --success-100: #dcfce7;
  --success-600: #16a34a;
  --success-700: #15803d;
  
  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-600: #d97706;
  --warning-700: #b45309;
  
  --danger-50: #fef2f2;
  --danger-100: #fee2e2;
  --danger-600: #dc2626;
  --danger-700: #b91c1c;
}

/* Layout Components */
.app-container {
  height: 100vh;
  display: flex;
  overflow: hidden;
  background: linear-gradient(135deg, var(--gray-50) 0%, #ffffff 50%, var(--gray-100) 100%);
}

/* Sidebar Styles */
.sidebar {
  width: 288px;
  background: linear-gradient(180deg, #ffffff 0%, var(--gray-50) 100%);
  border-right: 1px solid var(--gray-200);
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.sidebar-header {
  height: 80px;
  padding: 0 24px;
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 50%, var(--primary-800) 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15);
}

.sidebar-logo {
  font-size: 24px;
  font-weight: 700;
  color: white;
  letter-spacing: -0.025em;
}

.sidebar-logo span {
  color: var(--primary-200);
}

.sidebar-nav {
  flex: 1;
  padding: 24px 16px;
  overflow-y: auto;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 8px;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-1px);
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
}

.nav-item.active {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  color: white;
  box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1);
}

.nav-item.inactive {
  color: var(--gray-600);
}

.nav-icon {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  flex-shrink: 0;
}

.sidebar-footer {
  padding: 24px;
  border-top: 1px solid var(--gray-200);
  background: linear-gradient(135deg, var(--gray-50) 0%, #ffffff 100%);
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 14px;
  box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1);
}

.user-details {
  margin-left: 12px;
}

.user-name {
  font-weight: 600;
  font-size: 14px;
  color: var(--gray-900);
}

.user-role {
  font-size: 12px;
  color: var(--gray-500);
  text-transform: capitalize;
  font-weight: 500;
}

.logout-btn {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: none;
  border: none;
  border-radius: 12px;
  color: var(--gray-600);
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.8);
  color: var(--gray-900);
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
}

/* Main Content */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.5) 0%, rgba(255, 255, 255, 0.5) 100%);
}

.mobile-header {
  height: 80px;
  background: linear-gradient(135deg, #ffffff 0%, var(--gray-50) 100%);
  border-bottom: 1px solid var(--gray-200);
  display: none;
  align-items: center;
  padding: 0 24px;
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
}

.page-content {
  flex: 1;
  overflow-y: auto;
  padding: 32px;
}

.page-container {
  max-width: 1792px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Cards and Components */
.card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
  border: 1px solid rgba(226, 232, 240, 0.5);
  padding: 32px;
  transition: all 0.3s ease;
  animation: fadeIn 0.5s ease-in-out;
}

.card:hover {
  box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1);
}

.card-interactive {
  cursor: pointer;
}

.card-interactive:hover {
  transform: scale(1.02);
}

/* Page Headers */
.page-header {
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
  padding-bottom: 24px;
  margin-bottom: 32px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.8) 100%);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-700) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  transform: translateY(0);
}

.btn:hover {
  transform: translateY(-2px) scale(1.05);
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  color: white;
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-700) 0%, var(--primary-800) 100%);
  box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background: white;
  color: var(--gray-700);
  border: 1px solid var(--gray-200);
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
}

.btn-secondary:hover {
  background: var(--gray-50);
  box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1);
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger-600) 0%, var(--danger-700) 100%);
  color: white;
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
}

.btn-danger:hover {
  background: linear-gradient(135deg, var(--danger-700) 0%, #991b1b 100%);
  box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.btn:disabled:hover {
  transform: none;
}

/* Form Elements */
.input-field {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--gray-300);
  border-radius: 12px;
  font-size: 16px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.2s ease;
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
}

.input-field:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.form-label {
  display: block;
  font-weight: 600;
  font-size: 14px;
  color: var(--gray-700);
  margin-bottom: 8px;
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 50px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge-success {
  background: var(--success-100);
  color: var(--success-700);
  border: 1px solid var(--success-200);
}

.status-badge-warning {
  background: var(--warning-100);
  color: var(--warning-700);
  border: 1px solid var(--warning-200);
}

.status-badge-danger {
  background: var(--danger-100);
  color: var(--danger-700);
  border: 1px solid var(--danger-200);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    transform: translateY(10px); 
    opacity: 0; 
  }
  to { 
    transform: translateY(0); 
    opacity: 1; 
  }
}

@keyframes scaleIn {
  from { 
    transform: scale(0.95); 
    opacity: 0; 
  }
  to { 
    transform: scale(1); 
    opacity: 1; 
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.mb-2 { margin-bottom: 8px; }
.mb-4 { margin-bottom: 16px; }
.mb-6 { margin-bottom: 24px; }
.mb-8 { margin-bottom: 32px; }

.mt-2 { margin-top: 8px; }
.mt-4 { margin-top: 16px; }
.mt-6 { margin-top: 24px; }
.mt-8 { margin-top: 32px; }

.p-4 { padding: 16px; }
.p-6 { padding: 24px; }
.p-8 { padding: 32px; }

.space-y-4 > * + * { margin-top: 16px; }
.space-y-6 > * + * { margin-top: 24px; }
.space-y-8 > * + * { margin-top: 32px; }

.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

.gap-4 { gap: 16px; }
.gap-6 { gap: 24px; }
.gap-8 { gap: 32px; }

/* Statistics Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
  border: 1px solid rgba(226, 232, 240, 0.5);
  padding: 24px;
  transition: all 0.3s ease;
  animation: fadeIn 0.5s ease-in-out;
}

.stat-card:hover {
  box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon-container {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 24px;
  height: 24px;
}

.stat-details {
  flex: 1;
}

.stat-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-600);
  margin-bottom: 4px;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: var(--gray-900);
}

/* Table Styles */
.table {
  width: 100%;
  border-collapse: collapse;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
}

.table th {
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
  padding: 16px;
  text-align: left;
  font-weight: 600;
  font-size: 14px;
  color: var(--gray-700);
  border-bottom: 1px solid var(--gray-200);
}

.table td {
  padding: 16px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
  font-size: 14px;
}

.table tr:hover {
  background: rgba(248, 250, 252, 0.5);
}

/* Color Utilities */
.text-success-600 { color: var(--success-600); }
.text-warning-600 { color: var(--warning-600); }
.text-danger-600 { color: var(--danger-600); }
.text-primary-600 { color: var(--primary-600); }

/* Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    display: none;
  }
  
  .mobile-header {
    display: flex;
  }
  
  .page-content {
    padding: 16px;
  }
  
  .grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-4 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

/* Login Page Styles */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-50) 0%, #ffffff 50%, #fdf4ff 100%);
  padding: 48px 16px;
}

.login-card {
  max-width: 448px;
  width: 100%;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(226, 232, 240, 0.5);
  padding: 32px;
  animation: fadeIn 0.5s ease-in-out;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 32px;
  border-radius: 16px;
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15);
}

.login-title {
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary-600) 0%, #d946ef 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 16px;
}

.login-subtitle {
  color: var(--gray-600);
  font-size: 18px;
}

.login-form {
  margin-bottom: 32px;
}

.form-group {
  margin-bottom: 16px;
}

.password-input-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--gray-400);
  cursor: pointer;
  padding: 4px;
}

.password-toggle:hover {
  color: var(--gray-600);
}

.error-message {
  border-radius: 12px;
  background: linear-gradient(135deg, var(--danger-50) 0%, var(--danger-100) 100%);
  border: 1px solid var(--danger-200);
  padding: 16px;
  margin-bottom: 16px;
}

.error-text {
  color: var(--danger-700);
  font-weight: 500;
  font-size: 14px;
}

.demo-section {
  text-align: center;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.demo-credentials {
  font-size: 14px;
  color: var(--gray-600);
}

.demo-credential {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: var(--gray-100);
  padding: 6px 12px;
  border-radius: 8px;
  display: inline-block;
  margin: 4px;
}

/* Dashboard Styles */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: linear-gradient(135deg, #ffffff 0%, var(--gray-50) 100%);
  border-radius: 16px;
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
  border: 1px solid rgba(226, 232, 240, 0.5);
  padding: 24px;
  transition: all 0.3s ease;
  animation: slideUp 0.3s ease-out;
}

.stat-card:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon-container {
  padding: 16px;
  border-radius: 16px;
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
  margin-right: 20px;
}

.stat-icon {
  width: 28px;
  height: 28px;
}

.stat-details {
  flex: 1;
}

.stat-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: var(--gray-900);
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.quick-action-card {
  background: linear-gradient(135deg, #ffffff 0%, var(--gray-50) 100%);
  border-radius: 16px;
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
  border: 1px solid rgba(226, 232, 240, 0.5);
  padding: 24px;
  transition: all 0.3s ease;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
  display: block;
}

.quick-action-card:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15);
  text-decoration: none;
  color: inherit;
}

.action-content {
  display: flex;
  align-items: center;
}

.action-icon-container {
  padding: 16px;
  border-radius: 16px;
  margin-right: 20px;
  transition: all 0.3s ease;
}

.action-icon {
  width: 28px;
  height: 28px;
  color: white;
}

.action-details {
  flex: 1;
}

.action-title {
  font-size: 18px;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 8px;
  transition: color 0.3s ease;
}

.quick-action-card:hover .action-title {
  color: var(--primary-600);
}

.action-description {
  font-size: 14px;
  color: var(--gray-600);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 32px;
  margin-bottom: 32px;
}

.feature-card {
  background: linear-gradient(135deg, #ffffff 0%, var(--gray-50) 50%, #ffffff 100%);
  border-radius: 16px;
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
  border: 1px solid rgba(226, 232, 240, 0.5);
  padding: 32px;
  text-align: center;
  transition: all 0.5s ease;
}

.feature-card:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15);
}

.feature-icon-container {
  width: 64px;
  height: 64px;
  margin: 0 auto 24px;
  border-radius: 16px;
  background: linear-gradient(135deg, var(--primary-100) 0%, var(--primary-200) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
  transition: all 0.3s ease;
}

.feature-card:hover .feature-icon-container {
  box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  width: 32px;
  height: 32px;
  color: var(--primary-600);
  transition: transform 0.3s ease;
}

.feature-card:hover .feature-icon {
  transform: scale(1.1);
}

.feature-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 12px;
  transition: color 0.3s ease;
}

.feature-card:hover .feature-title {
  color: var(--primary-600);
}

.feature-description {
  font-size: 14px;
  color: var(--gray-600);
  line-height: 1.6;
}

/* Getting Started Card */
.getting-started-card {
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 50%, #fdf4ff 100%);
  border-radius: 16px;
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
  border: 1px solid rgba(14, 165, 233, 0.2);
  padding: 32px;
  margin-bottom: 32px;
  transition: all 0.3s ease;
  animation: fadeIn 0.5s ease-in-out;
}

.getting-started-card:hover {
  box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1);
}

.getting-started-content {
  display: flex;
  align-items: flex-start;
}

.getting-started-icon-container {
  width: 48px;
  height: 48px;
  border-radius: 16px;
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1);
  margin-right: 24px;
  flex-shrink: 0;
}

.getting-started-icon {
  width: 28px;
  height: 28px;
  color: white;
}

.getting-started-details {
  flex: 1;
}

.getting-started-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-900);
  margin-bottom: 12px;
}

.getting-started-description {
  color: var(--primary-700);
  line-height: 1.6;
  margin-bottom: 24px;
}

/* System Status */
.status-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: linear-gradient(135deg, var(--gray-50) 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.status-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--gray-700);
}

/* Table Styles */
.table-container {
  overflow-x: auto;
  border-radius: 12px;
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
}

.table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.table th {
  padding: 12px 24px;
  text-align: left;
  font-size: 12px;
  font-weight: 500;
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  background: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
}

.table td {
  padding: 16px 24px;
  border-bottom: 1px solid var(--gray-200);
  vertical-align: middle;
}

.table tr:hover {
  background: var(--gray-50);
}

.table tr:last-child td {
  border-bottom: none;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(107, 114, 128, 0.5);
  backdrop-filter: blur(4px);
  overflow-y: auto;
  height: 100%;
  width: 100%;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--gray-200);
  width: 100%;
  max-width: 400px;
  padding: 20px;
  animation: scaleIn 0.2s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.modal-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--gray-900);
}

.modal-close {
  background: none;
  border: none;
  color: var(--gray-400);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.modal-close:hover {
  color: var(--gray-600);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
}

/* File Upload Styles */
.file-upload-area {
  border: 2px dashed var(--gray-300);
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  transition: border-color 0.2s ease;
  cursor: pointer;
}

.file-upload-area:hover {
  border-color: var(--primary-400);
}

.file-upload-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto 16px;
  color: var(--gray-400);
}

.file-upload-text {
  font-size: 14px;
  color: var(--gray-600);
  margin-bottom: 8px;
}

.file-upload-hint {
  font-size: 12px;
  color: var(--gray-500);
}

.file-list {
  max-height: 128px;
  overflow-y: auto;
  margin-top: 16px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--gray-50);
  padding: 8px 12px;
  border-radius: 8px;
  margin-bottom: 4px;
}

.file-name {
  font-size: 14px;
  color: var(--gray-700);
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-remove {
  background: none;
  border: none;
  color: var(--danger-600);
  cursor: pointer;
  padding: 4px;
}

.file-remove:hover {
  color: var(--danger-800);
}

/* Progress Steps */
.progress-steps {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
}

.progress-step {
  display: flex;
  align-items: center;
  flex: 1;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 14px;
  margin-right: 8px;
}

.step-number.active {
  background: var(--primary-600);
  color: white;
}

.step-number.inactive {
  background: var(--gray-200);
  color: var(--gray-500);
}

.step-label {
  font-weight: 500;
  font-size: 14px;
}

.step-label.active {
  color: var(--primary-600);
}

.step-label.inactive {
  color: var(--gray-400);
}

.progress-bar {
  background: var(--gray-200);
  border-radius: 50px;
  height: 8px;
  margin-top: 16px;
  overflow: hidden;
}

.progress-fill {
  background: var(--primary-600);
  height: 100%;
  border-radius: 50px;
  transition: width 0.3s ease;
}

/* Success/Error Messages */
.success-message {
  border-radius: 12px;
  background: linear-gradient(135deg, var(--success-50) 0%, var(--success-100) 100%);
  border: 1px solid var(--success-200);
  padding: 16px;
  margin-bottom: 16px;
  display: flex;
  align-items: flex-start;
}

.success-icon {
  width: 20px;
  height: 20px;
  color: var(--success-400);
  margin-right: 12px;
  flex-shrink: 0;
}

.success-text {
  color: var(--success-700);
  font-weight: 500;
  font-size: 14px;
}

.error-message {
  border-radius: 12px;
  background: linear-gradient(135deg, var(--danger-50) 0%, var(--danger-100) 100%);
  border: 1px solid var(--danger-200);
  padding: 16px;
  margin-bottom: 16px;
  display: flex;
  align-items: flex-start;
}

.error-icon {
  width: 20px;
  height: 20px;
  color: var(--danger-400);
  margin-right: 12px;
  flex-shrink: 0;
}

/* Settings Page Styles */
.settings-container {
  display: grid;
  grid-template-columns: 1fr 3fr;
  gap: 24px;
}

.settings-nav {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.settings-nav-item {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: none;
  border: none;
  border-radius: 12px;
  font-weight: 500;
  font-size: 14px;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: var(--gray-600);
}

.settings-nav-item:hover {
  background: var(--gray-50);
  color: var(--gray-900);
}

.settings-nav-item.active {
  background: var(--primary-100);
  color: var(--primary-700);
  border: 1px solid var(--primary-200);
}

.settings-nav-icon {
  width: 20px;
  height: 20px;
  margin-right: 12px;
}

.settings-content {
  /* Content styles handled by card class */
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-flex;
  height: 24px;
  width: 44px;
  align-items: center;
  border-radius: 50px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.toggle-switch.active {
  background: var(--primary-600);
}

.toggle-switch.inactive {
  background: var(--gray-200);
}

.toggle-thumb {
  display: inline-block;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: white;
  transition: transform 0.2s ease;
}

.toggle-switch.active .toggle-thumb {
  transform: translateX(24px);
}

.toggle-switch.inactive .toggle-thumb {
  transform: translateX(4px);
}

/* User Avatar in Tables */
.user-avatar-table {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 500;
  font-size: 14px;
}

.user-info-table {
  display: flex;
  align-items: center;
}

.user-details-table {
  margin-left: 16px;
}

.user-name-table {
  font-weight: 500;
  font-size: 14px;
  color: var(--gray-900);
}

.user-email-table {
  font-size: 14px;
  color: var(--gray-500);
}

/* Role Badges */
.role-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 50px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid;
}

.role-badge.admin {
  background: #faf5ff;
  color: #7c3aed;
  border-color: #e9d5ff;
}

.role-badge.teacher {
  background: #eff6ff;
  color: #2563eb;
  border-color: #dbeafe;
}

.role-badge.student {
  background: #f0fdf4;
  color: #16a34a;
  border-color: #bbf7d0;
}

.role-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

/* Subject Tags */
.subject-tag {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  background: var(--gray-100);
  color: var(--gray-800);
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  margin: 2px;
}

/* Action Buttons in Tables */
.table-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 8px;
  background: none;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--gray-100);
}

.action-btn.edit {
  color: var(--primary-600);
}

.action-btn.edit:hover {
  background: var(--primary-50);
  color: var(--primary-900);
}

.action-btn.delete {
  color: var(--danger-600);
}

.action-btn.delete:hover {
  background: var(--danger-50);
  color: var(--danger-900);
}

.action-icon {
  width: 16px;
  height: 16px;
}

/* Loading States */
.loading-spinner {
  width: 48px;
  height: 48px;
  margin: 0 auto 16px;
  color: var(--primary-600);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  color: var(--gray-600);
  text-align: center;
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: 48px 24px;
}

.empty-state-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto 16px;
  color: var(--gray-400);
}

.empty-state-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-900);
  margin-bottom: 4px;
}

.empty-state-description {
  font-size: 14px;
  color: var(--gray-500);
  margin-bottom: 24px;
}
